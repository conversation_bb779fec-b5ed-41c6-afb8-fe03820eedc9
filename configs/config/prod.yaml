server:
  port: 80
  mode: release # gin的模式：debug, release, test
  log_level: info # 日志等级：debug, info, warn, error
  cors: # 跨域配置
    allow_origins:
      - "*"
    allow_headers:
      - "*"
    allow_credentials: false # 当 allow_origins为 * 时，allow_credentials 必须为 false

# 是否开发环境
dev: false
# goto加解密密钥
aes_key: KYt0sY3mN1OQ0TS6

mysql:
  host: **********
  port: 3306
  user: garden
  password: 2zyev03pW0mN28v
  db_name: garden

redis:
  host: redis
  port: 6379
  db: 0
  password:

alert:
  webhook_url: https://open.feishu.cn/open-apis/bot/v2/hook/0fbabe77-3605-416e-bc14-5b156e1d5386
  template_id: AAqBl4dTO6ihr
  template_version_name: 1.0.4
