# Garden

golang 1.24.4

go 代理设置：goproxy.cn

```bash
go env -w GO111MODULE=on
go env -w GOPROXY=https://goproxy.cn,direct
```

私有仓库不走代理
```shell
go env -w GOPRIVATE=github.com/gongyinshi/*
```

安装

```bash
# 安装 wire (wire 是 go 语言的依赖注入工具)
go install github.com/google/wire/cmd/wire@latest

# 安装air (air 是 go 语言的开发工具，用于热更新)
go install github.com/air-verse/air@latest

# 安装 swag (swag 是 go 语言的文档生成工具)
go install github.com/swaggo/swag/cmd/swag@latest

```

## 项目结构

```
├── cmd
│   ├── root.go           -- 命令行主函数
│   ├── server.go         -- http服务启动
│   ├── gen.go            -- model代码生成
│   ├── wire.go           -- 依赖注入
│   ├── wire_gen.go       -- 依赖注入生成
├── configs
│   ├── config.yaml       -- 配置文件
├── docs                  -- 文档(接口文档)
├── internal
│   ├── app
│   │   ├── app.go        -- 应用层
│   │   ├── wire_set.go   -- 依赖注入
│   ├── config
│   │   ├── config.go     -- 配置文件解析
│   ├── constant          -- 各个业务的常量
│   │   ├── context.go    -- 上下文相关的常量
│   │   ├── redis.go      -- redis相关的常量(redis的key在一个地方定义，避免分散定义导致key冲突)
│   ├── handler           -- 控制器
│   │   ├── user.go
│   │   ├── order.go
│   ├── router            -- 路由器
│   │   ├── middleware    -- 中间件
│   │   ├── router.go     -- 路由配置
│   │   ├── wire_set.go   -- 依赖注入
│   ├── service           -- 业务逻辑
│   ├── repo              -- 数据库操作
│   ├── model             -- 数据模型(由GORM生成)
│   ├── schema            -- API请求参数和响应参数（用于接口传输的数据结构）
│   ├── entity            -- 实体定义（一般用于内部）
│   ├── util              -- 工具类函数
├── pkg
│   ├── mysql             -- mysql库包装
│   ├── redis             -- redis库包装
├── main.go               -- 主函数
├── Makefile              -- 构建文件
├── .air.toml             -- air配置文件
├── README.md
```

## 项目运行

```bash
make run
```

## 项目构建

```bash
make build
```
