package main

import (
	"context"
	"garden/internal/app"
	"garden/internal/config"
	"garden/pkg/alert"
	"garden/pkg/db"
	"garden/pkg/rdb"
	"garden/pkg/slog"
	"log"
)

// @title           Garden接口文档
// @version         1.0
// @description     Garden接口文档
// @BasePath  /
func main() {
	app, cleanup, err := app.InitApp(context.Background())
	if err != nil {
		log.Fatalf("Failed to initialize app: %v", err)
	}
	defer cleanup()
	// 初始化 slog
	slog.Init(config.C.Server.LogLevel, alert.NewFeishuAlert(config.C.Alert.WebhookURL, config.C.Alert.TemplateID, config.C.Alert.TemplateVersionName))
	// 优雅关闭 数据库 连接
	defer db.Cleanup()
	// 优雅关闭 redis 连接
	defer rdb.Cleanup()

	// 启动 server
	app.Run()
}
