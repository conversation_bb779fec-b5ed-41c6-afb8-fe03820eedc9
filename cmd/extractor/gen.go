package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

const (
	root       = "internal/extractor"
	output     = "internal/extractor/register.go"
	projectPkg = "garden" // 替换成你项目的模块名（go.mod 中的 module 名）
)

func hasFunctionsInDir(dir string) (hasExtract, hasPlaylist, hasSubtitles bool, err error) {
	files, err := os.ReadDir(dir)
	if err != nil {
		return
	}

	fs := token.NewFileSet()

	for _, f := range files {
		if !strings.HasSuffix(f.Name(), ".go") || strings.HasSuffix(f.Name(), "_test.go") {
			continue
		}
		filePath := filepath.Join(dir, f.Name())
		node, err := parser.ParseFile(fs, filePath, nil, 0)
		if err != nil {
			return false, false, false, err
		}

		for _, decl := range node.Decls {
			if fn, ok := decl.(*ast.FuncDecl); ok && fn.Recv == nil {
				switch fn.Name.Name {
				case "Extract":
					hasExtract = true
				case "ExtractPlaylist":
					hasPlaylist = true
				case "ExtractSubtitles":
					hasSubtitles = true
				}
			}
		}
	}
	return
}

func main() {
	importSet := map[string]string{} // "partner/ake999": "ake999"
	groupedExtractors := map[string][]string{}
	groupedPlaylists := map[string][]string{}
	groupedSubtitles := map[string][]string{}

	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil || !info.IsDir() || path == root {
			return nil
		}

		hasPost, hasPlaylist, hasSubtitles, err := hasFunctionsInDir(path)
		if err != nil {
			return err
		}
		if !(hasPost || hasPlaylist || hasSubtitles) {
			return nil
		}

		rel, _ := filepath.Rel(root, path) // e.g. partner/ake999
		rel = filepath.ToSlash(rel)        // Ensure forward slashes
		pkg := filepath.Base(path)         // e.g. ake999
		importSet[rel] = pkg

		prefix := rel
		if idx := strings.Index(rel, "/"); idx != -1 {
			prefix = rel[:idx]
		}

		if hasPost {
			groupedExtractors[prefix] = append(groupedExtractors[prefix], fmt.Sprintf(`	extractors[%q] = %s.Extract`, rel, pkg))
		}
		if hasPlaylist {
			groupedPlaylists[prefix] = append(groupedPlaylists[prefix], fmt.Sprintf(`	playlistExtractors["playlist/%s"] = %s.ExtractPlaylist`, rel, pkg))
		}
		if hasSubtitles {
			groupedSubtitles[prefix] = append(groupedSubtitles[prefix], fmt.Sprintf(`	subtitlesExtractors["subtitles/%s"] = %s.ExtractSubtitles`, rel, pkg))
		}

		return nil
	})
	if err != nil {
		return
	}

	// Sort imports for consistent output
	var sortedImports []string
	for rel := range importSet {
		sortedImports = append(sortedImports, rel)
	}
	sort.Strings(sortedImports)

	var imports []string
	for _, rel := range sortedImports {
		imports = append(imports, fmt.Sprintf(`	"%s/%s/%s"`, projectPkg, filepath.ToSlash(root), rel))
	}

	// Compose grouped extractors
	composeGroup := func(groups map[string][]string) string {
		var lines []string
		var keys []string
		for k := range groups {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		for _, k := range keys {
			lines = append(lines, fmt.Sprintf("\t// %s", k))
			lines = append(lines, groups[k]...)
		}
		return strings.Join(lines, "\n")
	}

	content := fmt.Sprintf(`// Code generated by extractor/gen. DO NOT EDIT.
// Code generated by extractor/gen. DO NOT EDIT.
// Code generated by extractor/gen. DO NOT EDIT.

package extractor

import (
%s
)

func init() {
	// 单个帖子提取
%s

	// 播放列表/频道/主页批量提取
%s

	// 字幕提取
%s
}
`, strings.Join(imports, "\n"), composeGroup(groupedExtractors), composeGroup(groupedPlaylists), composeGroup(groupedSubtitles))
	//dir, err := os.Getwd()
	//if err != nil {
	//	log.Fatal(err)
	//}
	//fmt.Println("当前工作目录:", dir)
	f, err := os.Create(output)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	if err := os.WriteFile(output, []byte(content), 0644); err != nil {
		panic(err)
	}
	fmt.Println("✅ register_generated.go generated.")
}
