package main

import (
	"garden/pkg/db"
	"strings"

	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	defer db.Cleanup()

	g := gen.NewGenerator(gen.Config{
		OutPath:       "./internal/model/query",
		Mode:          gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
		FieldNullable: true,
	})
	// bigint => int64
	// tinyint(1) => bool
	// 其他int类型(int、mediumint、smallint、tinyint) => int
	g.WithDataTypeMap(map[string]func(columnType gorm.ColumnType) (dataType string){
		"int":       intTypeMap,
		"mediumint": intTypeMap,
		"smallint":  intTypeMap,
		"tinyint": func(columnType gorm.ColumnType) (dataType string) {
			ct, _ := columnType.ColumnType()
			// bool mapping
			if strings.HasPrefix(ct, "tinyint(1)") {
				if n, ok := columnType.Nullable(); ok && n {
					return "*bool"
				}
				return "bool"
			}
			// int mapping
			if n, ok := columnType.Nullable(); ok && n {
				return "*int"
			}
			return "int"
		},
	})
	g.UseDB(db.DB)

	// 指定需要生成数据表模型
	tables := []string{
		"extractor",
		"site_test_url",
	}
	models := make([]any, 0)
	for _, table := range tables {
		models = append(models, g.GenerateModel(table))
	}
	g.ApplyBasic(models...)

	g.Execute()
}

func intTypeMap(columnType gorm.ColumnType) (dataType string) {
	if n, ok := columnType.Nullable(); ok && n {
		return "*int"
	}
	return "int"
}
