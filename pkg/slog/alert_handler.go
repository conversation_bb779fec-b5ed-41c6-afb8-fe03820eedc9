package slog

import (
	"context"
	"fmt"
	"garden/pkg/alert"
	"log/slog"
)

type alertHandler struct {
	slog.Handler
	alert alert.Alert
}

func (h *alertHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.Handler.Enabled(ctx, level)
}

func (h *alertHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &alertHandler{Handler: h.Handler.WithAttrs(attrs)}
}

func (h *alertHandler) WithGroup(name string) slog.Handler {
	return &alertHandler{Handler: h.Handler.WithGroup(name)}
}

func (h *alertHandler) Handle(ctx context.Context, record slog.Record) error {
	if record.Level >= slog.LevelError {
		go h.sendErrorAlert(record)
	}
	return h.Handler.Handle(ctx, record)
}

func (h *alertHandler) sendErrorAlert(record slog.Record) {
	content := ""
	record.Attrs(func(attr slog.Attr) bool {
		content += fmt.Sprintf("%s: %+v\n", attr.Key, attr.Value.Any())
		return true
	})
	h.alert.Send(record.Message, content)
}
