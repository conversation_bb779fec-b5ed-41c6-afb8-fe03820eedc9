package slog

import (
	"garden/pkg/alert"
	"log/slog"
	"os"
	"strings"
)

func Init(levelInfo string, alert alert.Alert) {
	if levelInfo == "" {
		levelInfo = "info"
	}
	level := strings.ToLower(levelInfo)
	levelMap := map[string]slog.Level{
		"debug": slog.LevelDebug,
		"info":  slog.LevelInfo,
		"warn":  slog.LevelWarn,
		"error": slog.LevelError,
	}
	opts := slog.HandlerOptions{
		AddSource: false,
		Level:     levelMap[level],
	}
	logger := slog.New(&alertHandler{Handler: slog.NewJSONHandler(os.Stdout, &opts), alert: alert})
	slog.SetDefault(logger)
}
