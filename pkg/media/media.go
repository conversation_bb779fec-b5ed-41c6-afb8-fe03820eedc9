package media

import (
	"net/url"
	"path"
	"strings"

	"github.com/funnyfactor/mimetype"
)

type Type string

const (
	TypeVideo Type = "video"
	TypeImage Type = "image"
	TypeAudio Type = "audio"
)

// TypeByExtension 根据扩展名获取媒体类型
// 可能的媒体类型: video、image、audio、application、text等
func TypeByExtension(ext string) Type {
	if ext == "" {
		return ""
	}
	mimetype := mimetype.TypeByExtension(ext)
	if mimetype == "" {
		return ""
	}
	if mimetype == "application/x-mpegurl" || mimetype == "application/vnd.apple.mpegurl" { // .m3u8
		return TypeVideo
	}
	return Type(strings.Split(mimetype, "/")[0])
}

// TypeByURL 根据URL获取媒体类型
func TypeByURL(rawURL string) Type {
	// 从rawURL字符串中获取URL的 path部分
	u, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}
	return TypeByExtension(path.Ext(u.Path))
}
