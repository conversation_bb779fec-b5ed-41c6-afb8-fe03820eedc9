package extract

import "fmt"

type Error struct {
	Code      string
	Message   string
	Retryable bool
	Detail    string
}

func (e *Error) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

func New(code, message string, retryable bool) *Error {
	return &Error{Code: code, Message: message, Retryable: retryable}
}

// 携带详细错误信息
func ErrWithDetail(err *Error, detail string) *Error {
	newError := *err  // deep copy
	newError.Detail = detail
	return &newError
}

var (
	ErrUnknown    = New("Unknown", "发生未知错误，请稍后重试", true)        // 原错误码: -1
	ErrTimeout    = New("Timeout", "请求超时，请稍后重试", true)          // 原错误码: -8
	ErrInvalidURL = New("InvalidURL", "链接格式错误，请输入正确的链接", false) // 原错误码: -9
	// 以下错误都可以建议用户尝试客户端嗅探功能
	ErrExtractFailed              = New("ExtractFailed", "提取失败，请检查输入的链接是否正确", true)            // 原错误码: -10
	ErrUnsupportedURL             = New("UnsupportedURL", "请检查输入的链接是否正确", false)               // 原错误码: -11
	ErrNonPublicContent           = New("NonPublicContent", "暂不支持提取非公开内容", false)              // 原错误码: -12
	ErrLiveStreamNotSupported     = New("LiveStreamNotSupported", "暂不支持提取直播间(正在直播)的视频", false) // 原错误码: -14
	ErrPlaylistNotSupported       = New("PlaylistNotSupported", "暂不支持主页批量提取，请输入单个帖子链接", false) // 原错误码: -15
	ErrUserNotFound               = New("UserNotFound", "用户不存在，请检查输入的链接是否正确", false)           // 原错误码: -16
	ErrNoStory                    = New("NoStory", "用户没有正在显示的快拍", false)                       // 原错误码: -18
	ErrContentDeleted             = New("ContentDeleted", "请检查输入的链接内容是否已被删除", false)           // 原错误码: -19
	ErrInvalidUserPageURL         = New("InvalidUserPageURL", "请输入主页链接", false)                // 原错误码: -20
	ErrInvalidPlaylistURL         = New("InvalidPlaylistURL", "请输入公开的作者主页/频道页/播放列表页链接", false) // 原错误码: -21
	ErrUnsupportedSite            = New("UnsupportedSite", "暂不支持此链接，请联系客服反馈", false)           // 原错误码: -23
	ErrPremiumContentNotSupported = New("PremiumContentNotSupported", "暂不支持提取付费会员内容", false)   // 原错误码: -24
	ErrContentNotPremiered        = New("ContentNotPremiered", "内容还未上映", false)                // 原错误码: -25
	ErrRetryable                  = New("Retryable", "处理失败，请重试", true)                         // 原错误码: -30  (被反爬虫拦截)
)
