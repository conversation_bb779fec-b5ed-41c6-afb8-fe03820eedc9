package prom

import (
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-gonic/gin"
)

const (
	// GatewayCounter 网关提取总数
	GatewayCounter = "garden_gateway_counter"
	// GatewaySuccessCounter 网关提取成功总数
	GatewaySuccessCounter = "garden_gateway_success_counter"
	// ExtractorCounter 提取器提取总数
	ExtractorCounter = "garden_extractor_counter"
	// GatewayExtractorDuration 提取耗时
	GatewayExtractorDuration = "garden_gateway_duration_histogram"
	// ExtractorDuration 提取器提取耗时
	ExtractorDuration = "garden_extractor_duration_histogram"
)

// C Prometheus客户端实例
var C *ginprom.Prometheus

func init() {
	C = ginprom.New(
		ginprom.Subsystem(""),
		ginprom.Namespace(""),
		ginprom.Path("/internal/metrics"),
	)
	C.AddCustomCounter(GatewayCounter, "gateway counter", []string{"endpoint", "source"})
	C.AddCustomCounter(GatewaySuccessCounter, "gateway success counter", []string{"endpoint", "source"})
	C.AddCustomCounter(ExtractorCounter, "extractor counter", []string{"site", "extractor", "state"})
	C.AddCustomHistogram(GatewayExtractorDuration, "gateway extractor duration", []string{"site"})
	C.AddCustomHistogram(ExtractorDuration, "extractor duration", []string{"site", "node", "extractor"})
}

// Use 设置Gin引擎
func Use(e *gin.Engine) {
	C.Use(e)
}

// RecordGatewayDuration 记录网关提取耗时
func RecordGatewayDuration(site string, duration time.Duration) error {
	return C.AddCustomHistogramValue(GatewayExtractorDuration, []string{site}, float64(duration.Milliseconds()))
}

// RecordExtractorDuration 记录提取器耗时
func RecordExtractorDuration(site, node, extractor string, duration time.Duration) error {
	return C.AddCustomHistogramValue(ExtractorDuration, []string{site, node, extractor}, float64(duration.Milliseconds()))
}

// RecordDuration 通用记录耗时方法
func RecordDuration(metricName string, duration time.Duration, labels ...string) error {
	return C.AddCustomHistogramValue(metricName, labels, float64(duration.Milliseconds()))
}
