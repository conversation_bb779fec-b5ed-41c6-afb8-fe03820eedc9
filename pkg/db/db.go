package db

import (
	"garden/internal/config"
	"garden/internal/model/query"
	"log"

	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var Q *query.Query
var DB *gorm.DB

func init() {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.C.MySQL.User,
		config.C.MySQL.Password,
		config.C.MySQL.Host,
		config.C.MySQL.Port,
		config.C.MySQL.DBName,
	)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(fmt.Errorf("mysql connect failed: %w", err))
	}
	DB = db
	Q = query.Use(DB) // query.SetDefault(DB)
}

func Cleanup() {
	sqlDB, err := DB.DB()
	if err != nil {
		log.Println("Failed to get sql db", err)
	}
	sqlDB.Close()
}
