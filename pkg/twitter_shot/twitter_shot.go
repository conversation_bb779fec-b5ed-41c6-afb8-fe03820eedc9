package twitter_shot

import (
	"bytes"
	_ "embed"
	"encoding/json"
	"fmt"
	"garden/internal/config"
	"garden/internal/constant"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/fogleman/gg"
	"github.com/forPelevin/gomoji"
	"github.com/nfnt/resize"
	"github.com/rivo/uniseg"
	"github.com/srwiley/oksvg"
	"github.com/srwiley/rasterx"
)

const (
	W            = 2048
	avatarSize   = 160
	padding      = 160
	lineSpacing  = 50
	fontSize     = 60
	emojiSpacing = 30.0
)

type Client struct{}

func NewClient() *Client {
	return &Client{}
}

var hashtagPattern = regexp.MustCompile(`#[\p{L}\p{N}_]+`)
var atPattern = regexp.MustCompile(`@[\p{L}\p{N}_]+`)
var urlPattern = regexp.MustCompile(`https?://[^\s]+`)

//go:embed static/logo.svg
var XLogo []byte

//go:embed static/twitter_logo.png
var TwitterLogo []byte

//go:embed static/verified.png
var Verified []byte

type Payload struct {
	Fullname        string
	Username        string
	Verified        bool
	Text            string
	CreatedAt       string
	ProfileImageURL string // 可忽略，使用本地 static/twitter_logo.png
}

type Elem struct {
	Text     string
	IsEmoji  bool
	EmojiImg image.Image
	Width    float64
	IsBlue   bool
}

func decodeUnicodeText(s string) string {
	var out string
	_ = json.Unmarshal([]byte(`"`+s+`"`), &out)
	return out
}

// 下载并缓存 emoji 图片
func (c *Client) downloadEmojiImage(emoji string) image.Image {
	os.MkdirAll(config.C.TwitterShot.EmojiDir, 0755)
	safe := fmt.Sprintf("%x", []byte(emoji)) // 用 hex 表示文件名，避免非法字符
	localPath := filepath.Join(config.C.TwitterShot.EmojiDir, safe+".png")

	// 已存在缓存，直接加载
	if f, err := os.Open(localPath); err == nil {
		defer f.Close()
		img, _, err := image.Decode(f)
		if err == nil {
			return img
		}
	}

	// 下载 emoji 图标
	url := "https://emojicdn.elk.sh/" + emoji + "?style=twitter"
	resp, err := http.Get(url)
	if err != nil || resp.StatusCode != 200 {
		return nil
	}
	defer resp.Body.Close()

	outFile, err := os.Create(localPath)
	if err != nil {
		return nil
	}
	defer outFile.Close()

	body := io.TeeReader(resp.Body, outFile)
	img, _, err := image.Decode(body)
	if err != nil {
		return nil
	}
	return img
}

type blueSpan struct {
	start int
	end   int
}

// 拆分字符串为字符簇 Grapheme，包括复合 emoji
func (c *Client) splitTextToElems(dc *gg.Context, text string, fontSize float64) []Elem {
	type blueSpan struct {
		start int // byte offset
		end   int
	}
	var elems []Elem
	var spans []blueSpan

	// ✅ 使用 FindAllStringSubmatchIndex 获取 byte offset 范围
	findMatches := func(re *regexp.Regexp) {
		locs := re.FindAllStringIndex(text, -1)
		for _, loc := range locs {
			spans = append(spans, blueSpan{start: loc[0], end: loc[1]})
		}
	}
	findMatches(hashtagPattern)
	findMatches(atPattern)
	findMatches(urlPattern)

	// ✅ 判断 grapheme 是否处于任何高亮 span 内（字节级）
	isInBlueSpan := func(start, end int) bool {
		for _, span := range spans {
			if !(end <= span.start || start >= span.end) {
				return true
			}
		}
		return false
	}

	gr := uniseg.NewGraphemes(text)
	byteOffset := 0

	for gr.Next() {
		s := gr.Str()
		byteLen := len(s)
		start := byteOffset
		end := byteOffset + byteLen

		isBlue := isInBlueSpan(start, end)

		if gomoji.ContainsEmoji(s) {
			img := c.downloadEmojiImage(s)
			width := fontSize * 0.9
			elems = append(elems, Elem{
				Text:     s,
				IsEmoji:  true,
				EmojiImg: img,
				Width:    width,
			})
		} else {
			w, _ := dc.MeasureString(s)
			elems = append(elems, Elem{
				Text:    s,
				IsEmoji: false,
				Width:   w,
				IsBlue:  isBlue,
			})
		}

		byteOffset += byteLen
	}

	return elems
}

// 计算换行，考虑 emoji 加入间距后宽度
func layoutLines(elems []Elem, maxWidth float64) [][]Elem {
	var lines [][]Elem
	var currentLine []Elem
	var currentWidth float64

	for _, e := range elems {
		elemWidth := e.Width
		if e.IsEmoji {
			elemWidth += emojiSpacing // emoji 宽度加间隔
		}
		if currentWidth+elemWidth > maxWidth {
			if len(currentLine) > 0 {
				lines = append(lines, currentLine)
				currentLine = nil
				currentWidth = 0
			}
		}
		currentLine = append(currentLine, e)
		currentWidth += elemWidth
	}
	if len(currentLine) > 0 {
		lines = append(lines, currentLine)
	}
	return lines
}

func (c *Client) GenerateTwitterShot(payload Payload) (io.Reader, error) {
	tempDC := gg.NewContext(W, 100)
	if err := tempDC.LoadFontFace(config.C.TwitterShot.FontPath, fontSize); err != nil {
		return nil, fmt.Errorf("LoadFontFace: %v", err)
	}

	paragraphs := strings.Split(payload.Text, "\n")
	var allLines [][]Elem
	for _, para := range paragraphs {
		if strings.TrimSpace(para) == "" {
			allLines = append(allLines, []Elem{})
			continue
		}
		elems := c.splitTextToElems(tempDC, para, fontSize)
		lines := layoutLines(elems, float64(W)-padding*2)
		allLines = append(allLines, lines...)
	}
	fontHeight := tempDC.FontHeight()
	totalHeight := float64(len(allLines))*fontHeight + float64(len(allLines)-1)*lineSpacing

	headerHeight := avatarSize + 40
	height := padding + headerHeight + int(totalHeight) + int(lineSpacing) + 30 + padding*1.5

	dc := gg.NewContext(W, height)
	dc.SetRGB(1, 1, 1)
	dc.Clear()

	// ===== 绘制头像、昵称、用户名、认证图标 =====
	avatarImg, err := png.Decode(bytes.NewReader(TwitterLogo))
	if err != nil {
		return nil, fmt.Errorf("png.Decode: %v", err)
	}
	if payload.ProfileImageURL != "" {
		resp, err := constant.RestyClient.R().Get(payload.ProfileImageURL)
		if err != nil {
			return nil, fmt.Errorf("constant.RestyClient.R().Get: %v", err)
		}
		if resp.IsError() {
			return nil, fmt.Errorf("resp.Status: %v", resp.StatusCode())
		}
		avatarImg, err = jpeg.Decode(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("jpeg.Decode: %v", err)
		}
	}

	squareCanvas := gg.NewContext(avatarSize, avatarSize)
	squareCanvas.SetRGB(1, 1, 1)
	squareCanvas.Clear()

	bounds := avatarImg.Bounds()
	scale := float64(avatarSize) / float64(bounds.Dx())
	if bounds.Dy() > bounds.Dx() {
		scale = float64(avatarSize) / float64(bounds.Dy())
	}
	resizedW := uint(float64(bounds.Dx()) * scale)
	resizedH := uint(float64(bounds.Dy()) * scale)
	resizedAvatar := resize.Resize(resizedW, resizedH, avatarImg, resize.Lanczos3)

	offsetX := (avatarSize - int(resizedW)) / 2
	offsetY := (avatarSize - int(resizedH)) / 2
	squareCanvas.DrawImage(resizedAvatar, offsetX, offsetY)
	r := float64(avatarSize / 2)
	if payload.ProfileImageURL == "" {
		r = float64(avatarSize/2 + 10)
	}
	dc.DrawCircle(float64(padding+avatarSize/2), float64(padding+avatarSize/2-45), r)
	dc.Clip()
	dc.DrawImageAnchored(squareCanvas.Image(), padding+avatarSize/2, padding+avatarSize/2-45, 0.5, 0.5)
	dc.ResetClip()

	textX := padding + avatarSize + 20
	textY := padding + 15

	dc.SetRGB(0, 0, 0)
	if err := dc.LoadFontFace(config.C.TwitterShot.BoldFont, fontSize); err != nil {
		return nil, fmt.Errorf("LoadFontFace: %v", err)
	}
	dc.DrawStringAnchored(payload.Fullname, float64(textX), float64(textY), 0, 0)
	fullnameWidth, _ := dc.MeasureString(payload.Fullname)

	if payload.Verified {
		verifyImg, err := png.Decode(bytes.NewReader(Verified))
		if err != nil {
			return nil, fmt.Errorf("png.Decode: %v", err)
		}
		verifyResized := resize.Resize(fontSize, fontSize, verifyImg, resize.Lanczos3)
		dc.DrawImageAnchored(verifyResized, int(textX+int(fullnameWidth)+12), int(textY-20), 0, 0.5)
	}

	dc.SetRGB(0.4, 0.4, 0.4)
	if err := dc.LoadFontFace(config.C.TwitterShot.FontPath, fontSize); err != nil {
		return nil, fmt.Errorf("LoadFontFace: %v", err)
	}
	dc.DrawStringAnchored("@"+payload.Username, float64(textX), float64(textY+fontSize+lineSpacing/2), 0, 0)

	// ===== 绘制正文文本 + emoji 渲染 =====

	y := float64(padding + headerHeight + fontSize)
	if err := dc.LoadFontFace(config.C.TwitterShot.FontPath, fontSize); err != nil {
		return nil, fmt.Errorf("LoadFontFace: %v", err)
	}
	for _, line := range allLines {
		x := float64(padding)
		for i, elem := range line {
			if elem.IsEmoji && elem.EmojiImg != nil {
				img := elem.EmojiImg
				dc.DrawImageAnchored(img, int(x)+fontSize/2, int(y-fontSize*0.4), 0.5, 0.5)
				x += fontSize + 16.0
			} else {
				if elem.IsBlue == true {
					dc.SetRGB255(29, 155, 240)
				} else {
					dc.SetRGB(0, 0, 0)
				}
				dc.DrawStringAnchored(elem.Text, x, y, 0, 0)
				w, _ := dc.MeasureString(elem.Text)
				x += w
				// 如果下一个是 emoji，就额外增加间距
				if i+1 < len(line) && line[i+1].IsEmoji {
					x += 12.0
				}
			}
		}
		y += fontHeight + lineSpacing
	}
	// ===== 底部时间戳 =====
	dc.SetRGB(0.5, 0.5, 0.5)
	if err := dc.LoadFontFace(config.C.TwitterShot.FontPath, fontSize-5); err != nil {
		return nil, fmt.Errorf("LoadFontFace: %v", err)
	}
	timestampY := padding + headerHeight + 10 + int(totalHeight) + lineSpacing*3
	dc.DrawStringAnchored(payload.CreatedAt, float64(padding)+30, float64(timestampY), 0, 0)
	// ===== 右上角 X Logo (SVG) =====
	if err := drawSVG(dc, float64(W-padding-20), float64(padding+30), fontSize*2, fontSize*2); err != nil {
		panic(err)
	}

	// 输出图片
	var buf bytes.Buffer
	if err := png.Encode(&buf, dc.Image()); err != nil {
		return nil, fmt.Errorf("png.Encode: %v", err)
	}
	reader := bytes.NewReader(buf.Bytes())

	return reader, nil
}

func drawSVG(dc *gg.Context, x, y float64, targetWidth, targetHeight float64) error {
	icon, err := oksvg.ReadIconStream(bytes.NewReader(XLogo))
	if err != nil {
		return err
	}

	// 获取原始 SVG 的视图框尺寸
	viewBox := icon.ViewBox
	if viewBox.W == 0 || viewBox.H == 0 {
		return fmt.Errorf("invalid viewBox dimensions")
	}

	// 计算缩放比例，保持纵横比
	scaleX := targetWidth / viewBox.W
	scaleY := targetHeight / viewBox.H
	scale := min(scaleX, scaleY)

	// 计算缩放后的尺寸
	width := int(viewBox.W * scale)
	height := int(viewBox.H * scale)

	// 设置目标尺寸
	icon.SetTarget(0, 0, float64(width), float64(height))

	// 渲染 SVG 到图像
	rgba := image.NewRGBA(image.Rect(0, 0, width, height))
	scanner := rasterx.NewScannerGV(width, height, rgba, rgba.Bounds())
	dasher := rasterx.NewDasher(width, height, scanner)
	icon.Draw(dasher, 1)

	// 在指定位置居中绘制图像
	dc.DrawImageAnchored(rgba, int(x), int(y), 0.5, 0.5)
	return nil
}

//	func main() {
//		//  "😀Our Beautbbbbbbb55555555555🥰😗😙😚🙂5555nsvjdn看见你v开始加班就可看出可接受的你接口v的数据库但是v尽快打越さく良さん、カンペ見てこの喋りは〜〜〜\\nなに言いたいかも分からんし\\n見てられん😟脑v健康的VS空间v可接受的v接口bbbbbbbbbbbbbbbbbb\\nhttps://pbs.twimg.com/profile_images/1558667234855292929/RqgodvGb_200x200.jpg https://pbs.twimg.com/profile_images/1558667234855292929/RqgodvGb_200x200.jpg \\nbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbiful Client wearing our 24\\u201d Deepwave Unit\\ud83d\\ude29\\u2764\\ufe0f\\u2764\\ufe0f\\u2728\\n\\n@mimib_arbie @mimib_arbie thanks for trusting us\\u2764\\ufe0f\\u2764\\ufe0f\\u2728.\\n\\n#hair #m #model #holidays #hairstyles #hairstylist #heart #beautifuldestinations #beau #beautifulwoman #beachlife #travelphotography \\n\\n#i do not own rights to this song\\n오늘만큼은 성공한 배구덕후 뿌🍊 \\n첫 객원 해설도 완벽 그 자체✨\\n",
//		payload := Payload{
//			Fullname:        "测试用户",
//			Username:        "testuser",
//			Verified:        false,
//			Text:            "There’s so much love and \\nhappiness in my heart that \\nI only feel for you.....🧡🤡",
//			CreatedAt:       time.Now().Format("Jan 02,2006"),
//			ProfileImageURL: "https://pbs.twimg.com/profile_images/1558667234855292929/RqgodvGb_200x200.jpg", // 可忽略，使用本地 static/twitter_logo.png
//		}
//		GenerateTwitterShot(payload)
//	}
func GetTwitterProfilePic200x200(url string) string {
	if url == "" {
		return ""
	}
	return strings.Replace(url, "_normal", "_200x200", 1)
}
