package node

var Nodes = map[string]Node{
	"gz": {
		externalBaseURL: "http://**************:30080",
		internalBaseURL: "http://*********:30080",
	},
	"hk": {
		externalBaseURL: "http://**************:30080",
		internalBaseURL: "http://*********:30080",
	},
	"us-east": {
		externalBaseURL: "http://***********:30080",
		internalBaseURL: "http://*********:30080",
	},
	"las-vegas": {
		externalBaseURL: "https://garden-us-cdn.aipark.top",
	},
	"local": {
		externalBaseURL: "http://127.0.0.1:8000",
	},
}

var OverseasMainNode = Nodes["us-east"] // 海外主节点  短地址换长地址需要
var DomesticMainNode = Nodes["gz"]      // 国内主节点

type Node struct {
	externalBaseURL string // 节点默认访问地址
	internalBaseURL string // 内网优先访问的节点地址
}

func (n *Node) ExternalBaseURL() string {
	return n.externalBaseURL
}

func (n *Node) InternalBaseURL() string {
	if n.internalBaseURL == "" {
		return n.externalBaseURL
	}
	return n.internalBaseURL
}
