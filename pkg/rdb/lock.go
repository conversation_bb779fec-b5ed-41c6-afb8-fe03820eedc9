package rdb

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisLock struct {
	client  *redis.Client
	key     string
	timeout time.Duration
}

func NewRedisLock(key string, timeout time.Duration) *RedisLock {
	return &RedisLock{
		client:  C,
		key:     key,
		timeout: timeout,
	}
}

// 执行 fn 并在结束后自动释放锁
func (l *RedisLock) WithLock(ctx context.Context, fn func() error) error {
	ok, err := l.client.SetNX(ctx, l.key, "locked", l.timeout).Result()
	if err != nil {
		return err
	}
	if !ok {
		return nil
	}
	defer l.client.Del(ctx, l.key)
	return fn()
}

// 非阻塞方式尝试获取锁
func (l *RedisLock) TryLock(ctx context.Context) (bool, error) {
	return l.client.SetNX(ctx, l.key, "locked", l.timeout).Result()
}

// 手动释放锁
func (l *RedisLock) Unlock(ctx context.Context) error {
	return l.client.Del(ctx, l.key).Err()
}
