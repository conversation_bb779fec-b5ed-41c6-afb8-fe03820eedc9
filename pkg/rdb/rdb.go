package rdb

import (
	"context"
	"fmt"
	"garden/internal/config"
	"time"

	"github.com/redis/go-redis/v9"
)

// Redis客户端实例
var C *redis.Client

func init() {
	C = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.C.Redis.Host, config.C.Redis.Port),
		Password: config.C.Redis.Password,
		DB:       config.C.Redis.DB,
	})
	if err := C.Ping(context.Background()).Err(); err != nil {
		panic(fmt.Errorf("redis connect failed: %w", err))
	}
}

func Cleanup() {
	C.Close()
}

type RedisKey struct {
	Key        string
	Expiration time.Duration
}

var (
	RedisExtractIPCounter = RedisKey{
		Key:        "counter:ip:%s",
		Expiration: 1 * time.Hour,
	}
	RedisDownloadYtdlpLock = RedisKey{
		Key:        "lock:download_ytdlp",
		Expiration: 10 * time.Minute,
	}
	RedisInstagramUsernameMap = RedisKey{
		Key:        "instagram:username_map:%s",
		Expiration: 24 * time.Hour,
	}
	RedisExtractorSuccessCounter = RedisKey{
		Key: "counter:extractor_success",
	}
	RedisExtractorFailCounter = RedisKey{
		Key: "counter:extractor_fail",
	}
	RedisExtractorHistory = RedisKey{
		Key:        "extractor:history:%s",
		Expiration: 7 * 24 * time.Hour, // 7天过期
	}
	RedisGotoExtractResult = RedisKey{
		Key:        "goto:extract_result:%s",
		Expiration: 24 * time.Hour,
	}
)
