package alert

import (
	"fmt"
	"time"

	"resty.dev/v3"
)

type Feishu<PERSON>lert struct {
	webhookURL          string
	templateID          string
	templateVersionName string
	client              *resty.Client
}

func NewFeishuAlert(webhookURL string, templateID string, templateVersionName string) *FeishuAlert {
	return &FeishuAlert{
		webhookURL:          webhookURL,
		templateID:          templateID,
		templateVersionName: templateVersionName,
		client:              resty.New().SetTimeout(5 * time.Second),
	}
}

type feishuRequest struct {
	MsgType string         `json:"msg_type"`
	Card    map[string]any `json:"card"`
}

type feishuData struct {
	TemplateID          string            `json:"template_id"`
	TemplateVersionName string            `json:"template_version_name"`
	TemplateVariable    map[string]string `json:"template_variable"`
}

func (a *FeishuAlert) Send(title string, content string) error {
	body := feishuRequest{
		MsgType: "interactive",
		Card: map[string]any{
			"type": "template",
			"data": feishuData{
				TemplateID:          a.templateID,
				TemplateVersionName: a.templateVersionName,
				TemplateVariable: map[string]string{
					"title":      title,
					"body":       content,
					"alarm_time": time.Now().Format("2006-01-02 15:04:05"),
				},
			},
		},
	}
	resp, err := a.client.R().SetBody(body).Post(a.webhookURL)
	if err != nil {
		return err
	}
	if resp.IsError() {
		return fmt.Errorf("send feishu message failed, status code: %d, response: %s", resp.StatusCode(), resp.String())
	}
	return nil
}
