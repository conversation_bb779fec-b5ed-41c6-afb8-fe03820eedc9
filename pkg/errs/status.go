package errs

import (
	"net/http"
)

// ErrBusiness 业务错误
func ErrBusiness(message string) *ResponseError {
	return New(http.StatusBadRequest, message)
}

func ErrBusinessWithCode(message string, code string) *ResponseError {
	e := New(http.StatusBadRequest, message)
	e.Code = code
	return e
}

func ErrInvalidParam(detail string) *ResponseError {
	e := New(http.StatusUnprocessableEntity, "Invalid Parameter")
	e.Detail = detail
	return e
}

var (
	ErrUnauthorized        = New(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
	ErrPaymentRequired     = New(http.StatusPaymentRequired, http.StatusText(http.StatusPaymentRequired))
	ErrForbidden           = New(http.StatusForbidden, http.StatusText(http.StatusForbidden))
	ErrNotFound            = New(http.StatusNotFound, http.StatusText(http.StatusNotFound))
	ErrTooManyRequests     = New(http.StatusTooManyRequests, http.StatusText(http.StatusTooManyRequests))
	ErrInternalServerError = New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError))
	ErrBusinessGeneral     = New(http.StatusBadRequest, "operationFailure")
)
