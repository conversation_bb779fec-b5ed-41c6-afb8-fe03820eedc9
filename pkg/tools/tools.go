package tools

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net"
	"net/http"

	"strings"
)

func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// 获取客户端IP
func ClientIP(r *http.Request) string {
	// 优先检查 X-Forwarded-For
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		// X-Forwarded-For 可能是 "clientIP, proxy1, proxy2" 的形式，取第一个
		ips := strings.Split(forwarded, ",")
		return strings.TrimSpace(ips[0])
	}

	// 再检查 X-Real-IP
	if realIP := r.Header.Get("X-Real-IP"); realIP != "" {
		return realIP
	}

	// 最后 fallback 到 RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr // 如果解析失败，直接返回原始值
	}
	return ip
}

// XorEncrypt 使用XOR加密数据并返回Base64编码的字符串
func XorEncryptToBase64(data string, key string) string {
	dataBytes := []byte(data)
	keyBytes := []byte(key)
	result := make([]byte, len(dataBytes))

	for i := range dataBytes {
		result[i] = dataBytes[i] ^ keyBytes[i%len(keyBytes)]
	}

	return base64.StdEncoding.EncodeToString(result)
}

// XorDecryptFromBase64 解密Base64编码的XOR加密数据
func XorDecryptFromBase64(encryptedBase64 string, key string) (string, error) {
	// Base64解码
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedBase64)
	if err != nil {
		return "", err
	}

	keyBytes := []byte(key)
	result := make([]byte, len(encryptedBytes))

	for i := range encryptedBytes {
		result[i] = encryptedBytes[i] ^ keyBytes[i%len(keyBytes)]
	}

	return string(result), nil
}

// MustGetValueFromCtx 从上下文中获取值，理论上一定有值，如果没值说明服务端逻辑存在BUG
func MustGetValueFromCtx[T any](ctx context.Context, key string) T {
	value := ctx.Value(key)
	if value == nil {
		panic(fmt.Sprintf("value not found: %s", key))
	}
	return value.(T)
}
