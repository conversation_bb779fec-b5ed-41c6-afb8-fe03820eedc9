package handler

import (
	"errors"
	"garden/pkg/errs"
	"garden/pkg/extract"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ErrorResp struct {
	Message   string `json:"message"`
	Code      string `json:"code,omitempty"`
	Retryable bool   `json:"retryable,omitempty"`
	Detail    string `json:"detail,omitempty"`
}

func FailureResponse(c *gin.Context, err error) {
	// 提取相关的错误
	var extractErr *extract.Error
	if errors.As(err, &extractErr) {
		data := ErrorResp{Message: extractErr.Message, Code: extractErr.Code, Retryable: extractErr.Retryable, Detail: extractErr.Detail}
		c.JSON(http.StatusBadRequest, data)
		return
	}
	var responseErr *errs.ResponseError
	if validationErr, ok := err.(validator.ValidationErrors); ok { // 参数校验错误
		responseErr = errs.ErrInvalidParam(validationErr.Error())
	} else if responseErr, ok = err.(*errs.ResponseError); !ok { // 其他错误
		// 如果不是已知的错误类型，则返回内部服务器错误
		responseErr = errs.ErrInternalServerError
	}
	data := ErrorResp{Message: responseErr.Message, Code: responseErr.Code, Detail: responseErr.Detail}
	c.JSON(responseErr.HttpStatusCode, data)
}

func SuccessResponse(c *gin.Context, data any) {
	if data == nil {
		data = gin.H{}
	}
	c.JSON(http.StatusOK, data)
}
