package handler

import (
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"garden/pkg/useragent"

	"github.com/gin-gonic/gin"
)

type RedirectionHandler struct {
}

func NewRedirectionHandler() *RedirectionHandler {
	return &RedirectionHandler{}
}

func (h *RedirectionHandler) Unshorten(c *gin.Context) {
	var req schema.Unshorten
	if err := c.ShouldBindJSON(&req); err != nil {
		FailureResponse(c, err)
		return
	}
	url := redirect.ResolveFinalURL(c, req.URL, &redirect.Options{
		UserAgent: useragent.RandomDesktop(),
	})
	SuccessResponse(c, &schema.Unshorten{
		URL: url,
	})
}
