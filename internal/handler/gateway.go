package handler

import (
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service"
	"garden/pkg/errs"
	"garden/pkg/extract"
	"garden/pkg/prom"
	"log/slog"

	"github.com/gongyinshi/shared/urlx"

	"github.com/gin-gonic/gin"
)

type GatewayHandler struct {
	service *service.GatewayService
}

func NewGatewayHandler(service *service.GatewayService) *GatewayHandler {
	return &GatewayHandler{
		service: service,
	}
}

func (h *GatewayHandler) bindReq(c *gin.Context) (*schema.GatewayReq, error) {
	var req schema.GatewayReq
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("参数错误", slog.Any("err", err))
		return nil, errs.ErrInvalidParam(err.Error())
	}
	// 从文本中提取URL
	req.URL = urlx.FirstURL(req.URL)
	if req.URL == "" {
		return nil, extract.ErrInvalidURL
	}
	if req.Source == "" {
		req.Source = "unknown"
	}
	c.Set(constant.ContextRequestSource, req.Source)
	// 记录网关请求次数
	if err := prom.C.IncrementCounterValue(prom.GatewayCounter, []string{c.FullPath(), req.Source}); err != nil {
		slog.WarnContext(c, "数据上报异常", slog.Any("err", err))
	}
	return &req, nil
}

// 记录成功提取次数
func (h *GatewayHandler) successCountReport(c *gin.Context, source string, err error) {
	if err == nil {
		if err := prom.C.IncrementCounterValue(prom.GatewaySuccessCounter, []string{c.FullPath(), source}); err != nil {
			slog.WarnContext(c, "数据上报异常", slog.Any("err", err))
		}
	}
}

// ExtractPost 单个帖子提取
func (h *GatewayHandler) ExtractPost(c *gin.Context) {
	req, err := h.bindReq(c)
	if err != nil {
		FailureResponse(c, err)
		return
	}

	result, err := h.service.Post(c, req)
	h.successCountReport(c, req.Source, err)
	if err != nil {
		FailureResponse(c, err)
		return
	}
	SuccessResponse(c, result)
}

// ExtractPlaylist 播放列表提取
func (h *GatewayHandler) ExtractPlaylist(c *gin.Context) {
	req, err := h.bindReq(c)
	if err != nil {
		FailureResponse(c, err)
		return
	}

	result, err := h.service.Playlist(c, req)
	h.successCountReport(c, req.Source, err)
	if err != nil {
		FailureResponse(c, err)
		return
	}
	SuccessResponse(c, result)
}

// ExtractSubtitles 字幕提取
func (h *GatewayHandler) ExtractSubtitles(c *gin.Context) {
	req, err := h.bindReq(c)
	if err != nil {
		FailureResponse(c, err)
		return
	}

	result, err := h.service.Subtitle(c, &schema.GatewayReq{
		URL: req.URL,
	})
	h.successCountReport(c, req.Source, err)
	if err != nil {
		FailureResponse(c, err)
		return
	}

	SuccessResponse(c, result)
}
