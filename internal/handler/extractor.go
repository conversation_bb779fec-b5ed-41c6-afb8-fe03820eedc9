package handler

import (
	"context"
	"encoding/json"
	"errors"
	"garden/internal/constant"
	"garden/internal/extractor"
	"garden/internal/schema"
	"garden/pkg/errs"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/sites"
	"github.com/gongyinshi/shared/urlx"
	"log/slog"
	"net"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

type ExtractorHandler struct {
}

func NewExtractorHandler() *ExtractorHandler {
	return &ExtractorHandler{}
}

// Extract 单个帖子提取
func (h *ExtractorHandler) Extract(c *gin.Context) {
	defer recoverExtractPanic(c)
	req, ok := bindAndValidate(c)
	if !ok {
		return
	}
	req.URL = urlx.NormalizeURL(req.URL) // 将不支持的URL格式转换为常见格式
	result, err := extractor.Extract(c, req)
	if err != nil {
		handleExtractError(c, err, req)
		return
	}
	if result.IsEmpty() { // 判断帖子里的medias是否为空
		slog.Warn("单个帖子medias为空", slog.Any("req", req), slog.Any("result", result))
		FailureResponse(c, extract.ErrExtractFailed)
		return
	}
	setMediaRequestHeaders(sites.GetByURL(req.URL), result) // 设置媒体请求头
	slog.Info("[Post] 提取成功", slog.Any("req", req), slog.Any("result", result))
	SuccessResponse(c, result)
}

// ExtractPlaylist 播放列表提取
func (h *ExtractorHandler) ExtractPlaylist(c *gin.Context) {
	defer recoverExtractPanic(c)
	req, ok := bindAndValidate(c)
	if !ok {
		return
	}
	req.URL = urlx.NormalizeURL(req.URL) // 将不支持的URL格式转换为常见格式
	result, err := extractor.ExtractPlaylist(c, req)
	if err != nil {
		handleExtractError(c, err, req)
		return
	}
	// TODO: 对海外站增加 PreviewProxyURL
	setMediaRequestHeaders(sites.GetByURL(req.URL), result) // 设置媒体请求头
	slog.Info("[Playlist] 提取成功", slog.Any("req", req), slog.Any("result", result))
	SuccessResponse(c, result)
}

// ExtractSubtitles 字幕提取
func (h *ExtractorHandler) ExtractSubtitles(c *gin.Context) {
	defer recoverExtractPanic(c)
	req, ok := bindAndValidate(c)
	if !ok {
		return
	}
	result, err := extractor.ExtractSubtitles(c, req)
	if err != nil {
		handleExtractError(c, err, req)
		return
	}
	slog.Info("[Subtitles] 提取成功", slog.Any("req", req), slog.Any("result", result))
	SuccessResponse(c, result)
}

// bindAndValidate 绑定请求参数并验证
func bindAndValidate(c *gin.Context) (*schema.ExtractReq, bool) {
	var req schema.ExtractReq
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("参数错误", slog.Any("err", err))
		FailureResponse(c, errs.ErrInvalidParam(err.Error()))
		return nil, false
	}
	return &req, true
}

// recoverExtractPanic 捕获提取过程中的panic
func recoverExtractPanic(c *gin.Context) {
	if err := recover(); err != nil {
		slog.Error("提取过程出现panic", slog.Any("err", err), slog.String("stack", string(debug.Stack())))
		FailureResponse(c, extract.ErrExtractFailed)
	}
}

// handleExtractError 处理提取过程中的错误
func handleExtractError(c *gin.Context, err error, req *schema.ExtractReq) {
	if errors.As(err, new(*extract.Error)) {
		slog.Warn("提取失败", slog.Any("err", err), slog.Any("req", req))
		FailureResponse(c, err)
		return
	}
	// 判断是否是网络连接错误或请求超时错误
	if errors.As(err, new(*net.OpError)) || errors.Is(err, context.DeadlineExceeded) {
		slog.Error("提取失败，网络连接错误或请求超时", slog.Any("err", err), slog.Any("req", req)) // 告警
		FailureResponse(c, extract.ErrWithDetail(extract.ErrTimeout, err.Error()))
		return
	}
	// 判断是否是json反序列化错误: 接口的数据结构发生变化，需人工修复
	if errors.As(err, new(*json.UnmarshalTypeError)) {
		slog.Error("提取失败，json反序列化错误", slog.Any("err", err), slog.Any("req", req))
		FailureResponse(c, extract.ErrWithDetail(extract.ErrExtractFailed, err.Error()))
		return
	}
	// 其他错误
	slog.Warn("提取失败", slog.Any("err", err), slog.Any("req", req))
	FailureResponse(c, extract.ErrWithDetail(extract.ErrExtractFailed, err.Error()))
}

// setMediaRequestHeaders 设置媒体请求头
func setMediaRequestHeaders(site *sites.Site, result any) {
	var headers map[string]string = make(map[string]string)
	switch site {
	case sites.WEIBO, sites.DOUYIN, sites.KUAISHOU:
		headers[constant.HeaderUserAgent] = ""
	case sites.XIAOHONGSHU:
		headers[constant.HeaderReferer] = "https://www.xiaohongshu.com/"
	}
	if len(headers) > 0 {
		switch res := result.(type) {
		case *schema.Post:
			for _, media := range res.Medias {
				media.Headers = headers
			}
		case *schema.Playlist:
			for _, post := range res.Posts {
				for _, media := range post.Medias {
					media.Headers = headers
				}
			}
		}
	}
}
