package handler

import (
	"garden/internal/extractor"
	"garden/internal/repo"
	"garden/internal/schema"
	"garden/internal/service"
	"garden/internal/service/selector"
	"garden/pkg/errs"
	"garden/pkg/node"
	"github.com/gongyinshi/shared/sites"
	"log/slog"

	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

type ConsoleHandler struct {
	gatewayRepo         *repo.GatewayRepo
	extractorJobService *service.ExtractorJobService
	selector            *selector.Selector
}

func NewConsoleHandler(
	gatewayRepo *repo.GatewayRepo,
	extractorJobService *service.ExtractorJobService,
	selector *selector.Selector,
) *ConsoleHandler {
	return &ConsoleHandler{
		gatewayRepo:         gatewayRepo,
		extractorJobService: extractorJobService,
		selector:            selector,
	}
}

func (h *ConsoleHandler) GetExtractorMeta(ctx *gin.Context) {
	meta := make(map[string]any)
	meta["sites"] = sites.GetAllSiteIDs()
	meta["prioritys"] = []int{service.PriorityHigh, service.PriorityMedium, service.PriorityLow, service.PriorityFinal}
	meta["extractors"] = extractor.GetAllExtractor()
	meta["equivalents"] = []int{0, 1}
	nodes := make(map[string]any)
	for k, v := range node.Nodes {
		nodes[k] = map[string]string{
			"internalBaseURL": v.InternalBaseURL(),
			"externalBaseURL": v.ExternalBaseURL(),
		}
	}
	meta["nodes"] = nodes
	SuccessResponse(ctx, meta)
}

func (h *ConsoleHandler) ListExtractorRecords(ctx *gin.Context) {
	extractors := h.gatewayRepo.ListExtractors(ctx)
	list := make([]*schema.ListExtractorRecordsRespItem, 0, len(extractors))
	for _, m := range extractors {
		item := &schema.ListExtractorRecordsRespItem{
			ID:         m.ID,
			Extractor:  m.Extractor,
			Priority:   m.Priority,
			Equivalent: m.Equivalent,
		}
		for _, str := range gjson.Parse(m.NodeNames).Array() {
			item.NodeNames = append(item.NodeNames, str.String())
		}
		for _, str := range gjson.Parse(m.Sites).Array() {
			item.Sites = append(item.Sites, str.String())
		}
		list = append(list, item)
	}
	SuccessResponse(ctx, list)
}

func (h *ConsoleHandler) UpdateExtractorRecords(ctx *gin.Context) {
	var list []*schema.UpdateExtractorReqItem
	if err := ctx.ShouldBindJSON(&list); err != nil {
		FailureResponse(ctx, err)
		return
	}
	if err := h.gatewayRepo.UpdateExtractors(ctx, list); err != nil {
		slog.WarnContext(ctx, "保存提取器错误", slog.Any("err", err))
		FailureResponse(ctx, errs.ErrInternalServerError)
		return
	}
	SuccessResponse(ctx, nil)
}

func (h *ConsoleHandler) ListSiteTestURLs(ctx *gin.Context) {
	resp := h.gatewayRepo.ListSiteTestURLs(ctx)
	list := make([]*schema.UpdateSiteTestURLReqItem, 0, len(resp))
	for _, item := range resp {
		result := &schema.UpdateSiteTestURLReqItem{}
		for _, str := range gjson.Parse(item.Sites).Array() {
			result.Sites = append(result.Sites, str.String())
		}
		for _, str := range gjson.Parse(item.Urls).Array() {
			result.URLs = append(result.URLs, str.String())
		}
		list = append(list, result)
	}
	SuccessResponse(ctx, list)
}

func (h *ConsoleHandler) UpdateSiteTestURLs(ctx *gin.Context) {
	var list []*schema.UpdateSiteTestURLReqItem
	if err := ctx.ShouldBindJSON(&list); err != nil {
		FailureResponse(ctx, err)
		return
	}
	if err := h.gatewayRepo.UpdateSiteTestURLs(ctx, list); err != nil {
		slog.WarnContext(ctx, "保存站点测试URL错误", slog.Any("err", err))
		FailureResponse(ctx, errs.ErrInternalServerError)
		return
	}
	SuccessResponse(ctx, nil)
}

func (h *ConsoleHandler) GetSiteExtractorWeight(ctx *gin.Context) {
	extractors, _, err := h.selector.List(ctx)
	if err != nil {
		slog.WarnContext(ctx, "获取所有提取器错误", slog.Any("err", err))
		FailureResponse(ctx, errs.ErrInternalServerError)
		return
	}
	weightExtractors := make(map[string][]any)
	for _, e := range extractors {
		we := e.(*selector.WeightedExtractor)
		weightExtractors[we.Site()] = append(weightExtractors[we.Site()], map[string]any{
			"extractor":     we.Name(),
			"node_name":     we.Metadata()["nodeName"],
			"site":          we.Site(),
			"priority":      we.Priority(),
			"exclusion":     we.Metadata()["equivalent"],
			"system_weight": we.Weight(),
		})
	}
	SuccessResponse(ctx, weightExtractors)
}

// 计算所有提取器权重
func (h *ConsoleHandler) CalculateWeight(ctx *gin.Context) {
	err := h.extractorJobService.CalculateAllExtractorWeight(ctx)
	if err != nil {
		slog.WarnContext(ctx, "计算所有提取器权重错误", slog.Any("err", err))
		FailureResponse(ctx, errs.ErrInternalServerError)
		return
	}
	SuccessResponse(ctx, nil)
}
