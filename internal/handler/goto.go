package handler

import (
	"garden/internal/schema"
	"garden/internal/service"
	"garden/pkg/errs"
	"github.com/gin-gonic/gin"
	"net/http"
)

type GotoHandler struct {
	gotoService *service.GotoService
}

func NewGotoHandler(gotoService *service.GotoService) *GotoHandler {
	return &GotoHandler{
		gotoService: gotoService,
	}
}

func (h *GotoHandler) Goto(c *gin.Context) {
	var req schema.GotoReq
	if err := c.ShouldBindQuery(&req); err != nil {
		FailureResponse(c, errs.ErrInvalidParam(err.Error()))
		return
	}
	res, err := h.gotoService.Goto(c, req)
	if err != nil {
		FailureResponse(c, err)
		return
	}
	if res.Redirect {
		c.Redirect(http.StatusTemporaryRedirect, res.Post.Medias[0].ResourceURL)
		return
	}
	SuccessResponse(c, res.Post)
}
