package handler

import (
	"garden/internal/schema"
	"garden/internal/service"
	"garden/pkg/errs"

	"github.com/gin-gonic/gin"
)

type StreamHandler struct {
	gotoService *service.GotoService
}

func NewStreamHandler(gotoService *service.GotoService) *StreamHandler {
	return &StreamHandler{
		gotoService: gotoService,
	}
}

func (h *StreamHandler) Stream(c *gin.Context) {
	var req schema.StreamReq
	if err := c.ShouldBindQuery(&req); err != nil {
		FailureResponse(c, errs.ErrInvalidParam(err.Error()))
		return
	}
	if err := h.gotoService.Stream(c, req); err != nil {
		FailureResponse(c, err)
		return
	}
}
