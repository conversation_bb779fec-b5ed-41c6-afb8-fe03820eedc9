package app

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"garden/internal/config"
	"garden/internal/router"

	"github.com/gin-gonic/gin"
)

func InitGin() *gin.Engine {
	gin.SetMode(config.C.Server.Mode)
	engine := gin.New()
	return engine
}

type App struct {
	ctx    context.Context
	engine *gin.Engine
	router *router.Router
}

func NewApp(ctx context.Context, engine *gin.Engine, router *router.Router) *App {
	return &App{
		ctx:    ctx,
		engine: engine,
		router: router,
	}
}

func (a *App) Run() {
	a.router.SetupRoutes()

	server := &http.Server{
		Addr:              ":" + config.C.Server.Port,
		Handler:           a.engine,
		ReadHeaderTimeout: 5 * time.Second,
		IdleTimeout:       60 * time.Second,
	}

	go func() {
		err := server.ListenAndServe()
		if err == http.ErrServerClosed {
			log.Println("Server closed") // 正常关闭（不再接收新的请求）
		} else {
			log.Println("Server closed unexpectedly:", err) // 异常关闭（不再接收新的请求）
		}
	}()

	quit := make(chan os.Signal, 1)
	// kill (no params) by default sends syscall.SIGTERM
	// kill -2 is syscall.SIGINT
	// kill -9 is syscall.SIGKILL but can't be caught, so don't need add it
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	log.Println("Shutdown server ...")
	ctx, cancel := context.WithTimeout(a.ctx, 30*time.Second)
	defer cancel()
	// graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Println("Server exited:", err) // 超过最大等待时间退出（进程结束）
		return
	}
	log.Println("Server exited") // 正常退出（进程结束）
}
