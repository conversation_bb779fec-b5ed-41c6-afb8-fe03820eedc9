// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package app

import (
	"context"
	"garden/internal/handler"
	"garden/internal/repo"
	"garden/internal/router"
	"garden/internal/service"
)

// Injectors from wire.go:

// InitApp create a new App instance with all dependencies wired up
func InitApp(ctx context.Context) (*App, func(), error) {
	engine := InitGin()
	extractorHandler := handler.NewExtractorHandler()
	selector := service.NewSelector()
	gatewayRepo := repo.NewGatewayRepo()
	extractorJobService := service.NewExtractorJobService(selector, gatewayRepo)
	siteRepo := repo.NewSiteRepo()
	gatewayService := service.NewGatewayService(selector, extractorJobService, gatewayRepo, siteRepo)
	gatewayHandler := handler.NewGatewayHandler(gatewayService)
	redirectionHandler := handler.NewRedirectionHandler()
	consoleHandler := handler.NewConsoleHandler(gatewayRepo, extractorJobService, selector)
	aes, err := service.NewAES()
	if err != nil {
		return nil, nil, err
	}
	client := service.NewTwitterShotClient()
	gotoService := service.NewGotoService(aes, gatewayService, client)
	gotoHandler := handler.NewGotoHandler(gotoService)
	streamHandler := handler.NewStreamHandler(gotoService)
	routerRouter := router.NewRouter(engine, extractorHandler, gatewayHandler, redirectionHandler, consoleHandler, gotoHandler, streamHandler)
	app := NewApp(ctx, engine, routerRouter)
	return app, func() {
	}, nil
}
