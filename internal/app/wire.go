//go:build wireinject
// +build wireinject

package app

import (
	"context"
	"garden/internal/handler"
	"garden/internal/repo"
	"garden/internal/router"
	"garden/internal/service"

	"github.com/google/wire"
)

// InitApp create a new App instance with all dependencies wired up
func InitApp(ctx context.Context) (*App, func(), error) {
	wire.Build(
		InitGin,
		NewApp,
		router.ProviderSet,
		handler.ProviderSet,
		service.ProviderSet,
		repo.ProviderSet,
	)
	return &App{}, nil, nil
}
