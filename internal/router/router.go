package router

import (
	"garden/internal/config"
	"garden/internal/handler"
	"garden/internal/router/middleware"
	"garden/pkg/errs"
	"garden/pkg/prom"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

type Router struct {
	engine             *gin.Engine
	extractorHandler   *handler.ExtractorHandler
	gatewayHandler     *handler.GatewayHandler
	redirectionHandler *handler.RedirectionHandler
	consoleHandler     *handler.ConsoleHandler
	gotoHandler        *handler.GotoHandler
	streamHandler      *handler.StreamHandler
}

func NewRouter(
	engine *gin.Engine,
	extractorHandler *handler.ExtractorHandler,
	gatewayHandler *handler.GatewayHandler,
	redirectionHandler *handler.RedirectionHandler,
	consoleHandler *handler.ConsoleHandler,
	gotoHandler *handler.GotoHandler,
	streamHandler *handler.StreamHandler,
) *Router {
	router := &Router{
		engine:             engine,
		extractorHandler:   extractorHandler,
		gatewayHandler:     gatewayHandler,
		redirectionHandler: redirectionHandler,
		consoleHandler:     consoleHandler,
		gotoHandler:        gotoHandler,
		streamHandler:      streamHandler,
	}
	return router
}

func (r *Router) SetupRoutes() {
	// 自定义recovery中间件
	r.engine.Use(middleware.Recovery(), middleware.Common())
	// 404
	r.engine.NoRoute(func(c *gin.Context) {
		handler.FailureResponse(c, errs.ErrNotFound)
	})
	// 跨域
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = config.C.Server.Cors.AllowOrigins
	corsConfig.AllowHeaders = config.C.Server.Cors.AllowHeaders
	corsConfig.AllowCredentials = config.C.Server.Cors.AllowCredentials
	r.engine.Use(cors.New(corsConfig))
	// Prometheus Client
	prom.Use(r.engine)

	api := r.engine.Group("/")
	{
		// 单个帖子提取 (兼容Python版本的Gateway)  TODO: 废弃
		// api.POST("/extract", r.extractorHandler.Extract)

		// 提取器 (部署在不同节点，仅供网关调用)
		extract := api.Group("/extract")
		{
			// 单个帖子提取
			extract.POST("/post", r.extractorHandler.Extract)
			// 播放列表提取
			extract.POST("/playlist", r.extractorHandler.ExtractPlaylist)
			// 字幕提取
			extract.POST("/subtitles", r.extractorHandler.ExtractSubtitles)
		}

		// 网关(调度)
		gateway := api.Group("/gateway")
		{
			// 单个帖子提取
			gateway.POST("/post", r.gatewayHandler.ExtractPost)
			// 播放列表提取
			gateway.POST("/playlist", r.gatewayHandler.ExtractPlaylist)
			// 字幕提取
			gateway.POST("/subtitles", r.gatewayHandler.ExtractSubtitles)
		}

		// 重定向
		redirection := api.Group("/redirection")
		{
			// 短网址转长网址
			redirection.POST("/unshorten", r.redirectionHandler.Unshorten)
		}

		// 控制台
		console := api.Group("/console")
		{
			console.GET("/site-extractor-weight", r.consoleHandler.GetSiteExtractorWeight)
			console.GET("/extractor-meta", r.consoleHandler.GetExtractorMeta)
			console.GET("/extractor-records", r.consoleHandler.ListExtractorRecords)
			console.POST("/extractor-records", r.consoleHandler.UpdateExtractorRecords)
			console.GET("/site-test-urls", r.consoleHandler.ListSiteTestURLs)
			console.POST("/site-test-urls", r.consoleHandler.UpdateSiteTestURLs)
		}

		// 定时任务
		cron := api.Group("/cron")
		{
			cron.GET("/calculate-system-weight", r.consoleHandler.CalculateWeight)
		}

		// analyse
		analyse := api.Group("/analyse")
		{
			// goto
			analyse.GET("/goto", r.gotoHandler.Goto)
		}

		// stream
		stream := api.Group("/stream")
		{
			stream.GET("/", r.streamHandler.Stream)
		}

	}
}
