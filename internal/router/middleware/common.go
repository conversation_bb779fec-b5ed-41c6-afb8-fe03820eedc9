package middleware

import (
	"garden/internal/constant"
	"garden/pkg/tools"
	"github.com/gin-gonic/gin"
	"net/url"
)

func Common() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 客户端IP，一定有
		c.Set(constant.ContextIp, tools.ClientIP(c.Request))
		// 客户端UserAgent，如果客户端没传则为空字符串
		c.Set(constant.ContextUserAgent, c.Request.UserAgent())
		// 来源网站域名
		siteDomain := ""
		u, err := url.Parse(c.GetHeader(constant.HeaderOrigin))
		if err == nil {
			siteDomain = u.Hostname()
		}
		c.Set(constant.ContextSiteDomain, siteDomain)
		source := c.GetHeader(constant.HeaderRequestSource)
		if source != "" {
			c.Set(constant.ContextRequestSource, source)
		}
		c.Next()
	}
}
