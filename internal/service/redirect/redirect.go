package redirect

import (
	"context"
	"garden/internal/constant"
	"time"

	"github.com/samber/lo"
	"resty.dev/v3"
)

type Options struct {
	UserAgent   string        // Optional, Default: ""
	Timeout     time.Duration // Optional, Default: 3 seconds
	RestyClient *resty.Client // Optional, Default: global resty client
}

// ResolveFinalURL 解析多次重定向后的最终URL
// TODO: 待测试Instagram的短链接
func ResolveFinalURL(ctx context.Context, originalURL string, option *Options) string {
	option.RestyClient = lo.CoalesceOrEmpty(option.RestyClient, constant.RestyClient)
	option.Timeout = lo.CoalesceOrEmpty(option.Timeout, 3*time.Second)
	resp, err := option.RestyClient.R().SetContext(ctx).
		SetTimeout(option.Timeout).
		SetHeader("User-Agent", option.UserAgent).
		SetHeader("Connection", "close").
		Head(originalURL)
	if err != nil {
		return originalURL
	}
	if resp.IsError() {
		return originalURL
	}
	// 返回重定向后的最终URL
	return resp.RawResponse.Request.URL.String()
}

// ResolveFirstRedirectURL 解析第一个重定向URL
// 如指定 restyClient，restyClient 需配置 SetRedirectPolicy(resty.NoRedirectPolicy())
func ResolveFirstRedirectURL(ctx context.Context, originalURL string, option *Options) string {
	option.RestyClient = lo.CoalesceOrEmpty(option.RestyClient, constant.RestyClientNoRedirect)
	option.Timeout = lo.CoalesceOrEmpty(option.Timeout, 3*time.Second)
	resp, err := option.RestyClient.R().SetContext(ctx).
		SetTimeout(option.Timeout).
		SetHeader("User-Agent", option.UserAgent).
		SetHeader("Connection", "close").
		Head(originalURL)
	if err != nil {
		return originalURL
	}
	if resp.IsError() {
		return originalURL
	}
	// 返回第一个重定向URL
	return lo.CoalesceOrEmpty(resp.Header().Get("Location"), originalURL)
}
