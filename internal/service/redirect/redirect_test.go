package redirect

import (
	"context"
	"garden/pkg/useragent"
	"testing"
)

func TestResolveFinalURL(t *testing.T) {
	shortURL := "https://suno.com/s/0gYx1XowEIdHdEId"
	finalURL := ResolveFinalURL(context.Background(), shortURL, &Options{
		UserAgent: useragent.Desktop(),
	})
	t.Log(finalURL)
}

func TestResolveFirstRedirectURL(t *testing.T) {
	shortURL := "https://suno.com/s/0gYx1XowEIdHdEId"
	finalURL := ResolveFirstRedirectURL(context.Background(), shortURL, &Options{
		UserAgent: useragent.Desktop(),
	})
	t.Log(finalURL)
}
