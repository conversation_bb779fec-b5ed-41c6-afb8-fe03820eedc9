package selector

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"log/slog"
	"net/http"
	"net/url"
	"time"

	"github.com/tidwall/gjson"
)

type Extractor interface {
	ID() string

	InternalAddress() string

	ExternalAddress() string

	Address() string

	Name() string

	Site() string

	Priority() int

	Metadata() map[string]string

	Invoke(ctx context.Context, path string, req *schema.GatewayReq, reply any) error
}

type DefaultExtractor struct {
	id              string
	internalAddress string
	externalAddress string
	name            string
	site            string
	priority        int
	metadata        map[string]string
}

func NewExtractor(id, internalAddress, externalAddress, name, site string, priority int, metadata map[string]string) Extractor {
	return &DefaultExtractor{
		id:              id,
		internalAddress: internalAddress,
		externalAddress: externalAddress,
		name:            name,
		site:            site,
		priority:        priority,
		metadata:        metadata,
	}
}

func (d *DefaultExtractor) ID() string {
	return d.id
}

func (d *DefaultExtractor) InternalAddress() string {
	return d.internalAddress
}

func (d *DefaultExtractor) ExternalAddress() string {
	return d.externalAddress
}

func (d *DefaultExtractor) Address() string {
	if d.internalAddress != "" {
		return d.internalAddress
	}
	return d.externalAddress
}

func (d *DefaultExtractor) Name() string {
	return d.name
}

func (d *DefaultExtractor) Site() string {
	return d.site
}

func (d *DefaultExtractor) Priority() int {
	return d.priority
}

func (d *DefaultExtractor) Metadata() map[string]string {
	return d.metadata
}

func (d *DefaultExtractor) Invoke(ctx context.Context, path string, req *schema.GatewayReq, reply any) (err error) {
	extractReq := schema.ExtractReq{
		URL:       req.URL,
		Extractor: d.Name(),
		Cursor:    req.Cursor,
		Source:    ctx.Value(constant.ContextRequestSource).(string),
	}
	reqURL, err := url.JoinPath(d.Address(), path)
	if err != nil {
		slog.WarnContext(ctx, "join path failed", "err", err)
		return err
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(map[string]string{
			"Content-Type": "application/json",
		}).
		SetBody(extractReq).
		SetTimeout(45 * time.Second).
		Post(reqURL)
	if err != nil {
		return errors.New("retryable error")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusBadRequest {
			var extractErr extract.Error
			extractErr.Message = gjson.Get(resp.String(), "message").String()
			extractErr.Retryable = gjson.Get(resp.String(), "retryable").Bool()
			extractErr.Code = gjson.Get(resp.String(), "code").String()
			return &extractErr
		}
		return fmt.Errorf("status_code:%d, text:%s", resp.StatusCode(), resp.String())
	}
	err = json.NewDecoder(resp.Body).Decode(&reply)
	if err != nil {
		slog.Warn("decode response failed", "err", err)
		return err
	}
	return nil
}
