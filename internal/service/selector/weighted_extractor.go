package selector

import (
	"context"
	"encoding/json"
	"fmt"
	"garden/pkg/rdb"
	"log/slog"
	"strconv"
	"sync/atomic"
	"time"

	"github.com/redis/go-redis/v9"
)

type WeightedExtractorBuilder struct {
	weight *int
}

func NewWeightedExtractorBuilder() *WeightedExtractorBuilder {
	return &WeightedExtractorBuilder{}
}

func (b *WeightedExtractorBuilder) SetWeight(weight int) *WeightedExtractorBuilder {
	b.weight = &weight
	return b
}

func (b *WeightedExtractorBuilder) Build(n Extractor) *WeightedExtractor {
	e := &WeightedExtractor{
		Extractor:       n,
		cachedWeight:    &atomic.Value{},
		maxHistorySize:  100,
		decayHalfLife:   10 * time.Minute, // 10分钟衰减一半
		silentThreshold: 1 * time.Minute,  // 1分钟静默阈值
		lastEventTime:   time.Now().UnixNano(),
	}
	if b.weight != nil {
		e.cachedWeight.Store(&nodeWeight{
			updateAt: time.Now().UnixNano(),
			value:    *b.weight,
		})
	} else {
		e.initWeight()
	}
	return e
}

const defaultWeight = 10

type nodeWeight struct {
	value    int
	updateAt int64
}

type EventRecord struct {
	Success   bool  `json:"success"`
	Timestamp int64 `json:"timestamp"`
}

type WeightedExtractor struct {
	Extractor
	cachedWeight *atomic.Value
	// 配置参数
	maxHistorySize  int           // 最大历史记录数
	decayHalfLife   time.Duration // 衰减半衰期，多少时间内的事件影响更大
	lastEventTime   int64         // 最后一次事件发生的时间戳
	silentThreshold time.Duration // 静默阈值，多少时间没有事件就不重新计算
}

func (e *WeightedExtractor) Pick() DoneFunc {
	return func(ctx context.Context, di DoneInfo) {
		now := time.Now().UnixNano()

		// 创建事件记录
		event := EventRecord{
			Success:   di.Err == nil,
			Timestamp: now,
		}

		// 将事件存储到Redis
		if err := e.addEventToRedis(ctx, event); err != nil {
			slog.Warn("存储事件历史到Redis失败", "err", err, "extractorID", e.ID())
		}

		// 更新最后事件时间
		e.lastEventTime = now

		// 原有的Redis统计逻辑保持不变
		if di.Err == nil {
			if err := rdb.C.HIncrBy(ctx, rdb.RedisExtractorSuccessCounter.Key, e.ID(), 1).Err(); err != nil {
				slog.Warn("成功次数加+1失败", "err", err)
			}
		} else {
			if err := rdb.C.HIncrBy(ctx, rdb.RedisExtractorFailCounter.Key, e.ID(), 1).Err(); err != nil {
				slog.Warn("失败次数+1失败", "err", err)
			}
		}
	}
}

func (e *WeightedExtractor) Raw() Extractor {
	return e.Extractor
}

func (e *WeightedExtractor) initWeight() {
	e.cachedWeight.Store(&nodeWeight{
		updateAt: time.Now().UnixNano(),
		value:    defaultWeight,
	})
}

// 将事件添加到Redis
func (e *WeightedExtractor) addEventToRedis(ctx context.Context, event EventRecord) error {
	// 序列化事件数据
	eventData, err := json.Marshal(event)
	if err != nil {
		return err
	}

	// 使用有序集合存储，时间戳作为score
	key := e.getRedisHistoryKey()

	// 添加事件到有序集合
	if err := rdb.C.ZAdd(ctx, key, redis.Z{
		Score:  float64(event.Timestamp),
		Member: string(eventData),
	}).Err(); err != nil {
		return err
	}

	// 限制历史记录数量，只保留最新的N条记录
	if err := rdb.C.ZRemRangeByRank(ctx, key, 0, int64(-e.maxHistorySize-1)).Err(); err != nil {
		slog.Warn("清理历史记录失败", "err", err)
	}

	// 设置过期时间（7天）
	if err := rdb.C.Expire(ctx, key, 7*24*time.Hour).Err(); err != nil {
		slog.Warn("设置历史记录过期时间失败", "err", err)
	}

	return nil
}

// 从Redis获取事件历史
func (e *WeightedExtractor) getEventHistoryFromRedis(ctx context.Context) ([]EventRecord, error) {
	key := e.getRedisHistoryKey()

	// 获取最近7天的事件（按时间戳范围查询）
	recentThreshold := 7 * 24 * time.Hour
	minScore := float64(time.Now().Add(-recentThreshold).UnixNano())

	// 从有序集合中获取数据
	results, err := rdb.C.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: strconv.FormatFloat(minScore, 'f', 0, 64),
		Max: "+inf",
	}).Result()

	if err != nil {
		return nil, err
	}

	// 解析事件数据
	events := make([]EventRecord, 0, len(results))
	for _, result := range results {
		var event EventRecord
		if err := json.Unmarshal([]byte(result), &event); err != nil {
			slog.Warn("解析事件数据失败", "err", err, "data", result)
			continue
		}
		events = append(events, event)
	}

	return events, nil
}

// 获取Redis历史记录的key
func (e *WeightedExtractor) getRedisHistoryKey() string {
	return fmt.Sprintf(rdb.RedisExtractorHistory.Key, e.ID())
}

// 修改：从Redis计算时间加权权重
func (e *WeightedExtractor) calculateTimeWeightedScore(ctx context.Context) int {
	// 从Redis获取事件历史
	eventHistory, err := e.getEventHistoryFromRedis(ctx)
	if err != nil {
		slog.Warn("从Redis获取事件历史失败", "err", err, "extractorID", e.ID())
		return defaultWeight
	}

	if len(eventHistory) == 0 {
		return defaultWeight
	}

	now := time.Now().UnixNano()
	var weightedSuccess, totalWeight float64

	// 计算衰减常数
	decayConstant := 0.693147 / float64(e.decayHalfLife.Nanoseconds())

	for _, event := range eventHistory {
		timeDiff := float64(now - event.Timestamp)

		// 计算指数衰减权重
		weight := 1.0
		if timeDiff > 0 {
			weight = 1.0 / (1.0 + decayConstant*timeDiff/1e9)
		}

		totalWeight += weight
		if event.Success {
			weightedSuccess += weight
		}
	}

	if totalWeight == 0 {
		return defaultWeight
	}

	// 计算加权成功率
	weightedSuccessRate := weightedSuccess / totalWeight
	weight := int(weightedSuccessRate * 100)

	// 确保权重在合理范围内
	if weight < 1 {
		weight = 1
	} else if weight > 100 {
		weight = 100
	}

	return weight
}

func (e *WeightedExtractor) ResetWeight() {
	e.initWeight()
}

func (e *WeightedExtractor) UpdateWeight(successCount, failCount int) {
	ctx := context.Background()

	// 检查最近是否有新事件发生
	now := time.Now().UnixNano()
	timeSinceLastEvent := time.Duration(now - e.lastEventTime)

	// 如果超过静默阈值没有新事件，直接返回，不更新权重
	if timeSinceLastEvent > e.silentThreshold {
		slog.Debug("跳过权重更新",
			"id", e.ID(),
			"name", e.Name(),
			"timeSinceLastEvent", timeSinceLastEvent,
			"silentThreshold", e.silentThreshold,
			"reason", "超过静默阈值，保持现有权重不变")
		return
	}

	// 保留原有接口的传统计算
	var traditionalValue int
	if (successCount + failCount) != 0 {
		traditionalValue = int((float64(successCount) / float64(successCount+failCount)) * 100)
	} else {
		traditionalValue = defaultWeight
	}

	// 获取事件历史数量
	eventHistory, err := e.getEventHistoryFromRedis(ctx)
	historySize := 0
	if err == nil {
		historySize = len(eventHistory)
	}

	var finalValue int

	// 在静默阈值内有新事件，使用时间加权计算
	if historySize > 0 {
		timeWeightedValue := e.calculateTimeWeightedScore(ctx)

		// 根据历史记录数量决定使用哪种计算方式
		if historySize < 10 {
			// 历史记录较少时，传统计算和时间加权的加权平均
			finalValue = (traditionalValue + timeWeightedValue*2) / 3
		} else {
			// 历史记录充足时，主要使用时间加权
			finalValue = timeWeightedValue
		}

		slog.Debug("使用时间加权更新权重",
			"id", e.ID(),
			"name", e.Name(),
			"successCount", successCount,
			"failCount", failCount,
			"traditionalWeight", traditionalValue,
			"timeWeightedValue", timeWeightedValue,
			"finalWeight", finalValue,
			"historySize", historySize,
			"timeSinceLastEvent", timeSinceLastEvent)
	} else {
		// 没有历史记录，使用传统计算
		finalValue = traditionalValue

		slog.Debug("使用传统计算更新权重",
			"id", e.ID(),
			"name", e.Name(),
			"successCount", successCount,
			"failCount", failCount,
			"finalWeight", finalValue,
			"reason", "无历史记录，使用传统计算")
	}

	e.cachedWeight.Store(&nodeWeight{
		updateAt: now,
		value:    finalValue,
	})
}

// 辅助函数：计算绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func (e *WeightedExtractor) Weight() int {
	w, ok := e.cachedWeight.Load().(*nodeWeight)
	if !ok {
		e.initWeight()
		w = e.cachedWeight.Load().(*nodeWeight)
	}

	return w.value
}
