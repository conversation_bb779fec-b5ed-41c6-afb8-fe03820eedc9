package selector

import (
	"context"
	"errors"
	"sync/atomic"
)

type ExtractorFilter func(context.Context, []Extractor) []Extractor

type DoneInfo struct {
	Err     error
	ReplyMD map[string]string
}
type DoneFunc func(ctx context.Context, di DoneInfo)

var ErrNoAvailable = errors.New("no_available_extractor")

type Selector struct {
	NodeBuilder *WeightedExtractorBuilder
	Balancer    *Balancer

	nodes atomic.Value
}

func (d *Selector) Select(ctx context.Context, opts ...SelectOption) (selected Extractor, done DoneFunc, err error) {
	var (
		options    SelectOptions
		candidates []*WeightedExtractor
	)
	nodes, ok := d.nodes.Load().([]*WeightedExtractor)
	if !ok {
		return nil, nil, ErrNoAvailable
	}
	for _, o := range opts {
		o(&options)
	}
	// 执行过滤
	if len(options.ExtractorFilters) > 0 {
		newExtractors := make([]Extractor, len(nodes))
		for i, wc := range nodes {
			newExtractors[i] = wc
		}
		for _, filter := range options.ExtractorFilters {
			newExtractors = filter(ctx, newExtractors)
		}
		candidates = make([]*WeightedExtractor, len(newExtractors))
		for i, n := range newExtractors {
			candidates[i] = n.(*WeightedExtractor)
		}
	} else {
		candidates = nodes
	}
	if len(candidates) == 0 {
		return nil, nil, ErrNoAvailable
	}
	// 调用随机袋获取提取器
	we, done, err := d.Balancer.Pick(ctx, candidates)
	if err != nil {
		return nil, nil, err
	}
	return we.Raw(), done, nil
}

func (d *Selector) List(ctx context.Context, opts ...SelectOption) ([]Extractor, []DoneFunc, error) {
	var (
		options    SelectOptions
		candidates []*WeightedExtractor
	)
	nodes, ok := d.nodes.Load().([]*WeightedExtractor)
	if !ok {
		return nil, nil, ErrNoAvailable
	}
	for _, o := range opts {
		o(&options)
	}
	// 执行过滤
	if len(options.ExtractorFilters) > 0 {
		newExtractors := make([]Extractor, len(nodes))
		for i, wc := range nodes {
			newExtractors[i] = wc
		}
		for _, filter := range options.ExtractorFilters {
			newExtractors = filter(ctx, newExtractors)
		}
		candidates = make([]*WeightedExtractor, len(newExtractors))
		for i, n := range newExtractors {
			candidates[i] = n.(*WeightedExtractor)
		}
	} else {
		candidates = nodes
	}
	if len(candidates) == 0 {
		return nil, nil, ErrNoAvailable
	}
	exreactors := make([]Extractor, 0, len(candidates))
	dones := make([]DoneFunc, 0, len(candidates))
	for _, we := range candidates {
		exreactors = append(exreactors, we)
		dones = append(dones, we.Pick())
	}
	return exreactors, dones, nil
}

func (d *Selector) Apply(ctx context.Context, nodes []Extractor) {
	weightedNodes := make([]*WeightedExtractor, 0, len(nodes))
	for _, n := range nodes {
		if we, ok := n.(*WeightedExtractor); ok {
			// 如果节点本身就是WeightedExtractor，直接使用
			weightedNodes = append(weightedNodes, we)
		} else {
			// 否则通过builder创建WeightedExtractor
			weightedNodes = append(weightedNodes, d.NodeBuilder.Build(n))
		}
	}
	d.nodes.Store(weightedNodes)
}
