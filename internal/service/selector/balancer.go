package selector

import (
	"context"
	"log/slog"
	"math/rand"
	"sort"
)

// Balancer 按权重随机获取一个节点，权重越大，命中的概率越大
type Balancer struct{}

func NewBalancer() *Balancer {
	return &Balancer{}
}

func (p *Balancer) Pick(ctx context.Context, nodes []*WeightedExtractor) (*WeightedExtractor, DoneFunc, error) {
	if len(nodes) == 0 {
		return nil, nil, ErrNoAvailable
	}
	total := 0
	for _, node := range nodes {
		total += node.Weight()
	}
	sort.SliceStable(nodes, func(i, j int) bool { // 权重递增
		return nodes[i].Weight() < nodes[j].Weight()
	})
	cur := rand.Intn(total-nodes[0].Weight()+1) + nodes[0].Weight()
	indexNodeCur := 0
	for i, node := range nodes {
		indexNodeCur += node.Weight()
		if indexNodeCur >= cur {
			selected := nodes[i]
			slog.DebugContext(ctx, "选择提取器", "indexNodeCur", indexNodeCur, "id", selected.ID(), "name", selected.Name(), "weight", selected.Weight())
			return selected, selected.Pick(), nil
		}
	}
	return nil, nil, ErrNoAvailable
}
