package filter

import (
	"context"
	"garden/internal/service/selector"
	"github.com/gongyinshi/shared/sites"
	"slices"
)

// PriorityFilter 只获取优先级为 priority 的 extractor
func PriorityFilter(priority int) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if n.Priority() == priority {
				filtered = append(filtered, n)
			}
		}
		return filtered
	}
}

// ExtractorFilter 需要忽略的提取器
func ExtractorFilter(filterExtractors ...selector.Extractor) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		if len(filterExtractors) == 0 {
			return extractors
		}
		var filtered []selector.Extractor
		for _, n := range extractors {
			if !slices.ContainsFunc(filterExtractors, func(s selector.Extractor) bool {
				if n.Name() == s.Name() {
					return true
				}
				return false
			}) {
				filtered = append(filtered, n)
			}
		}
		return filtered
	}
}

// SiteFilter 站点过滤器
func SiteFilter(site sites.Site) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if n.Site() == site.ID {
				filtered = append(filtered, n)
			}
		}
		return filtered
	}
}

// EquivalentFilter 过滤掉能力一致的提取器
func EquivalentFilter(filterExtractors ...selector.Extractor) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		if len(filterExtractors) == 0 {
			return extractors
		}
		var filtered []selector.Extractor
		for _, n := range extractors {
			if !slices.ContainsFunc(filterExtractors, func(s selector.Extractor) bool {
				if s.Metadata()["exclusion"] == "1" && n.Name() == s.Name() {
					return true
				}
				return false
			}) {
				filtered = append(filtered, n)
			}
		}
		return filtered
	}
}

// NetworkFilter 过滤掉不需要的网络
func NetworkFilter(network string) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if n.Metadata()["network"] == network {
				filtered = append(filtered, n)
			}
		}
		return filtered
	}
}

func WeightGTFilter(weight int) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if we, ok := n.(*selector.WeightedExtractor); ok {
				if we.Weight() > weight {
					filtered = append(filtered, n)
				}
			}
		}
		return filtered
	}
}

func WeightGTQFilter(weight int) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if we, ok := n.(*selector.WeightedExtractor); ok {
				if we.Weight() >= weight {
					filtered = append(filtered, n)
				}
			}
		}
		return filtered
	}
}

func WeightLTFilter(weight int) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if we, ok := n.(*selector.WeightedExtractor); ok {
				if we.Weight() < weight {
					filtered = append(filtered, n)
				}
			}
		}
		return filtered
	}
}

func WeightLTQFilter(weight int) selector.ExtractorFilter {
	return func(ctx context.Context, extractors []selector.Extractor) []selector.Extractor {
		var filtered []selector.Extractor
		for _, n := range extractors {
			if we, ok := n.(*selector.WeightedExtractor); ok {
				if we.Weight() <= weight {
					filtered = append(filtered, n)
				}
			}
		}
		return filtered
	}
}
