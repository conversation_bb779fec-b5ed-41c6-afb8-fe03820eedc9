package service

import (
	"context"
	"encoding/json"
	"fmt"
	"garden/internal/config"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/errs"
	"garden/pkg/node"
	"garden/pkg/rdb"
	"garden/pkg/twitter_shot"
	"github.com/gongyinshi/shared/aes"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

const gotoSource = "batch_api"

type GotoService struct {
	gatewayService *GatewayService
	tsg            *twitter_shot.Client
	aes            *aes.AES
}

func NewGotoService(
	aes *aes.AES,
	gatewayService *GatewayService,
	tsg *twitter_shot.Client,
) *GotoService {
	return &GotoService{
		gatewayService: gatewayService,
		tsg:            tsg,
		aes:            aes,
	}
}

func (s *GotoService) Goto(c *gin.Context, req schema.GotoReq) (*schema.GotoResp, error) {
	payloadStr, err := s.aes.Decrypt(req.Payload)
	if err != nil {
		return nil, errs.ErrInvalidParam(err.Error())
	}

	var payload schema.Payload
	err = json.Unmarshal([]byte(payloadStr), &payload)
	if err != nil {
		return nil, errs.ErrInvalidParam(err.Error())
	}
	if time.Now().Unix() > payload.GetExpireTime() {
		return nil, errs.ErrInvalidParam("url expired")
	}
	if req.D && payload.GetURL() != "" {
		return &schema.GotoResp{
			Redirect: true,
			URL:      payload.GetURL(),
		}, nil
	}
	var cacheKey string
	switch payload.GetType() {
	case constant.PayloadSinglePostPayload:
		cacheKey = payload.GetURL()
	case constant.PayloadBilibiliAcgPayload:
		cacheKey = fmt.Sprintf("%v%v", payload.Get("bvid"), payload.Get("cid"))
	case constant.PayloadBilibiliBangumiPayload:
		cacheKey = fmt.Sprintf("%v%v%v%v", payload.Get("bvid"), payload.Get("cid"), payload.Get("aid"), payload.Get("ep_id"))
	default:
		return nil, errs.ErrInvalidParam("goto payload type not support")
	}
	result, err := s.extract(c, cacheKey, payload)
	if err != nil {
		return nil, err
	}
	if req.J {
		return &schema.GotoResp{
			Redirect: false,
			Post:     result,
		}, nil
	}
	return &schema.GotoResp{
		Redirect: false,
		URL:      result.Medias[0].ResourceURL,
	}, nil
}

func (s *GotoService) extract(ctx context.Context, cacheKey string, payload schema.Payload) (result *schema.Post, err error) {
	if res, err := rdb.C.Get(ctx, fmt.Sprintf(rdb.RedisGotoExtractResult.Key, cacheKey)).Result(); res != "" && err == nil {
		err = json.Unmarshal([]byte(res), &result)
		if err == nil {
			return result, nil
		}
	}
	defer func() {
		if err == nil { // 设置缓存
			cacheVal, _ := json.Marshal(result)
			_, rdbErr := rdb.C.Set(ctx, fmt.Sprintf(rdb.RedisGotoExtractResult.Key, cacheKey), cacheVal, rdb.RedisGotoExtractResult.Expiration).Result()
			if rdbErr != nil {
				slog.WarnContext(ctx, "设置goto提取缓存", slog.Any("err", rdbErr))
			}
		}
	}()
	switch payload.GetType() {
	case constant.PayloadSinglePostPayload:
		result, err = s.gatewayService.Post(ctx, &schema.GatewayReq{
			URL:    payload.GetURL(),
			Source: gotoSource,
		})
	case constant.PayloadBilibiliAcgPayload:
		result, err = GetAcgPlayInfo(ctx, cast.ToString(payload.Get("bvid")), cast.ToString(payload.Get("cid")), "", "")
	case constant.PayloadBilibiliBangumiPayload:
		result, err = GetPgcPlayInfo(ctx, cast.ToString(payload.Get("bvid")), cast.ToString(payload.Get("cid")), cast.ToString(payload.Get("aid")), cast.ToString(payload.Get("ep_id")), "", "")
	default:
		return nil, errs.ErrInvalidParam("goto payload type not support")
	}
	return
}

func (s *GotoService) GenerateGotoURL(payload schema.Payload, direct bool, optionQuery map[string]string) (string, error) {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	url := fmt.Sprintf("https://goto.feeprint.com/analyse/goto?payload=%v", s.aes.Encrypt(string(payloadBytes)))
	if direct {
		url += "&d=1"
	}
	for k, v := range optionQuery {
		url += fmt.Sprintf("&%v=%v", k, v)
	}
	return url, nil
}

func (s *GotoService) Stream(c *gin.Context, req schema.StreamReq) error {
	payloadStr, err := s.aes.Decrypt(req.Payload)
	if err != nil {
		return errs.ErrInvalidParam(err.Error())
	}
	var payload schema.Payload
	err = json.Unmarshal([]byte(payloadStr), &payload)
	if err != nil {
		return errs.ErrInvalidParam(err.Error())
	}
	if time.Now().Unix() > payload.GetExpireTime() {
		return errs.ErrInvalidParam("url expired")
	}
	switch payload.GetType() {
	case constant.PayloadTwitterScreenshotPayload:
		r, err := s.tsg.GenerateTwitterShot(twitter_shot.Payload{
			Fullname:        cast.ToString(payload.Get("fullname")),
			Username:        cast.ToString(payload.Get("username")),
			Verified:        cast.ToBool(payload.Get("verified")),
			Text:            cast.ToString(payload.Get("text")),
			CreatedAt:       cast.ToString(payload.Get("created_at")),
			ProfileImageURL: cast.ToString(payload.Get("profile_image_url")),
		})
		if err != nil {
			slog.WarnContext(c, "生成推特截图", slog.Any("err", err))
			return errs.ErrInvalidParam("")
		}
		c.DataFromReader(http.StatusOK, -1, "image/png", r, nil)
	case constant.PayloadStreamPayload:
		// 复制原始请求头
		headers := http.Header{}
		for k, v := range c.Request.Header {
			headers[k] = v
		}

		// 删除一些不需要的头
		for _, headerKey := range []string{"host", "referer", "accept-encoding", "remote-user"} {
			headers.Del(headerKey)
		}

		// 添加payload中指定的头
		for k, v := range payload.GetHeaders() {
			headers.Set(strings.ToLower(k), v)
		}

		// 创建HTTP请求
		httpReq, err := http.NewRequestWithContext(c, c.Request.Method, payload.GetURL(), nil)
		if err != nil {
			return errs.ErrInvalidParam(err.Error())
		}
		httpReq.Header = headers

		// 发起请求
		resp, err := http.DefaultClient.Do(httpReq)
		if err != nil {
			return errs.ErrInvalidParam(err.Error())
		}
		defer resp.Body.Close()

		// 复制所有响应头到gin context
		for k, v := range resp.Header {
			for _, value := range v {
				c.Header(k, value)
			}
		}
		// 响应数据
		c.DataFromReader(resp.StatusCode, resp.ContentLength, resp.Header.Get("Content-Type"), resp.Body, nil)
	}
	return nil
}

func (s *GotoService) GenerateStreamURL(payload schema.Payload, cdn bool) (string, error) {
	var nodeBaseURL string
	if cdn {
		nodeBaseURL = "https://garden-us-cdn.aipark.top"
	} else {
		n, ok := node.Nodes[config.C.Server.NodeName]
		if !ok {
			slog.WarnContext(context.Background(), "生成stream url", slog.String("node_name", config.C.Server.NodeName), slog.Any("err", "node not found"))
			return "", errs.ErrInternalServerError
		}
		nodeBaseURL = n.ExternalBaseURL()
	}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	url := fmt.Sprintf("%v/stream?payload=%v", nodeBaseURL, s.aes.Encrypt(string(payloadBytes)))
	return url, nil
}
