package service

import (
	"garden/internal/config"
	"garden/internal/service/selector"
	"github.com/gongyinshi/shared/aes"

	"garden/pkg/twitter_shot"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	NewSelector,
	NewGatewayService,
	NewExtractorJobService,
	NewAES,
	NewGotoService,
	NewBilibiliService,
	NewTwitterShotClient,
)

func NewSelector() *selector.Selector {
	return &selector.Selector{
		NodeBuilder: selector.NewWeightedExtractorBuilder(),
		Balancer:    selector.NewBalancer(),
	}
}

func NewAES() (*aes.AES, error) {
	return aes.NewAES(config.C.AESKey)
}

func NewTwitterShotClient() *twitter_shot.Client {
	return twitter_shot.NewClient()
}
