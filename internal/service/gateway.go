package service

import (
	"context"
	"errors"
	"fmt"
	"garden/internal/constant"
	"garden/internal/repo"
	"garden/internal/schema"
	"garden/internal/service/selector"
	"garden/internal/service/selector/filter"
	"garden/pkg/extract"
	"garden/pkg/media"
	"garden/pkg/node"
	"garden/pkg/prom"
	"log/slog"
	"slices"
	"time"

	"github.com/gongyinshi/shared/sites"

	"github.com/samber/lo"
)

const (
	SourceSnapanyWeb     = "snapany_web"
	SourceIIILabWeb      = "iiilab_web"
	SourceHengHengMaoAPI = "henghengmao_api"
	SourceMediaPlatform  = "mediaplatform" // 公众号
	SourceUnknown        = "unknown"
)

const (
	PriorityFinal = -1
	PriorityHigh  = iota
	PriorityMedium
	PriorityLow
)

const (
	extractTypePost     = "post"
	extractTypePlaylist = "playlist"
	extractTypeSubtitle = "subtitles"
)

type GatewayService struct {
	selector    *selector.Selector
	gatewayRepo *repo.GatewayRepo
	siteRepo    *repo.SiteRepo
	jobService  *ExtractorJobService
}

func NewGatewayService(s *selector.Selector, jobService *ExtractorJobService, gatewayRepo *repo.GatewayRepo, siteRepo *repo.SiteRepo) *GatewayService {
	service := &GatewayService{
		selector:    s,
		gatewayRepo: gatewayRepo,
		siteRepo:    siteRepo,
		jobService:  jobService,
	}
	return service
}

func (s *GatewayService) Post(ctx context.Context, req *schema.GatewayReq) (*schema.Post, error) {
	// 如果是媒体文件直接返回
	if post := s.isDirectMediaURL(req.URL); post != nil {
		return post, nil
	}

	// 短网址 转 长网址
	req.URL = s.Unshorten(ctx, req.URL)
	// 根据URL判断站点
	site := s.siteRepo.GetSiteByURL(ctx, req.URL)

	source := ctx.Value(constant.ContextRequestSource).(string)

	// 限制 iiilab_web 的链接类型
	if source == SourceIIILabWeb && !s.siteRepo.IsIIIlabWebSupportedSite(ctx, site) {
		return nil, extract.ErrUnsupportedSite
	}
	// 限制 snapany_web 的链接类型
	if source == SourceSnapanyWeb && !s.siteRepo.IsSnapanyWebSupportedSite(ctx, site) {
		return nil, extract.ErrUnsupportedSite
	}
	// 路由到免费/付费/PRO版本提取器
	site = s.siteEnumRouter(ctx, site, source)
	var res *schema.Post
	var err error
	err = s.extract(ctx, extractTypePost, *site, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *GatewayService) Playlist(ctx context.Context, req *schema.GatewayReq) (*schema.Playlist, error) {
	// 短网址 转 长网址
	req.URL = s.Unshorten(ctx, req.URL)
	// 根据URL获取播放列表站点
	site := lo.CoalesceOrEmpty(sites.SitePlaylistMap[sites.GetByURL(req.URL)], sites.YJJX_PLAYLIST)

	var res *schema.Playlist
	var err error
	err = s.extract(ctx, extractTypePlaylist, *site, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *GatewayService) Subtitle(ctx context.Context, req *schema.GatewayReq) (*schema.SubtitlesResp, error) {
	/*// 根据URL获取字幕站点
	site := sites.SiteSubtitlesMap[sites.GetByURL(req.URL)]
	if site == nil {
		FailureResponse(c, extract.ErrUnsupportedSite)
		return
	}*/
	// 短网址 转 长网址
	req.URL = s.Unshorten(ctx, req.URL)
	site := lo.CoalesceOrEmpty(sites.SiteSubtitlesMap[sites.GetByURL(req.URL)], sites.YOUTUBE_SUBTITLES)
	var res *schema.SubtitlesResp
	var err error
	err = s.extract(ctx, extractTypeSubtitle, *site, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// extract 提取内容
func (s *GatewayService) extract(ctx context.Context, extractType string, site sites.Site, req *schema.GatewayReq, res any) (err error) {
	startTime := time.Now()
	defer func() {
		if err != nil {
			return
		}
		// 记录提取总耗时
		if err := prom.RecordGatewayDuration(site.ID, time.Since(startTime)); err != nil {
			slog.WarnContext(ctx, "记录网关提取耗时异常", slog.Any("err", err))
		}
	}()
	// 根据site提取
	var (
		priority         = PriorityHigh
		target           selector.Extractor
		extractorHistory []selector.Extractor
		done             selector.DoneFunc
	)
	var callCount = 0
	for {
		if callCount >= constant.MaxRetryCount {
			return extract.ErrExtractFailed
		}
		target, done, err = s.selector.Select(ctx,
			selector.WithExtractorFilter(
				filter.SiteFilter(site),                      // 过滤其他站的提取器
				filter.PriorityFilter(priority),              // 只获取优先级==priority的提取器
				filter.ExtractorFilter(extractorHistory...),  // 过滤掉上次选择的提取器
				filter.EquivalentFilter(extractorHistory...), // 过滤能力一致的提取器
			),
		)
		if err != nil {
			if errors.Is(err, selector.ErrNoAvailable) {
				if priority == PriorityFinal {
					// 一个可用的执行器都没找，退出
					return extract.ErrExtractFailed
				} else if priority == PriorityLow {
					priority = PriorityFinal
				} else {
					priority++
				}
				continue
			}
			callCount++ // 其他错误次数+1，防止死循环
			continue
		}
		if target != nil {
			callCount++
			extractorHistory = append(extractorHistory, target)
		}
		slog.DebugContext(ctx, "提取器", slog.String("id", target.ID()), slog.String("name", target.Name()), slog.String("site", target.Site()), slog.Any("metadata", target.Metadata()), slog.Int("priority", target.Priority()))
		extractorInvokeStartTime := time.Now()
		err = target.Invoke(ctx, "/extract/"+extractType, req, res)
		if err != nil {
			slog.WarnContext(ctx, "提取器调用错误", slog.Any("err", err))
			done(ctx, selector.DoneInfo{Err: err})
			continue
		}
		// 提取记录耗时
		if err := prom.RecordExtractorDuration(site.ID, target.Metadata()["nodeName"], target.Name(), time.Since(extractorInvokeStartTime)); err != nil {
			slog.WarnContext(ctx, "记录耗时异常", slog.Any("err", err))
		}
		done(ctx, selector.DoneInfo{})
		break
	}
	return nil
}

// isDirectMediaURL 根据URL判断是否为媒体文件，如果是则返回帖子
func (s *GatewayService) isDirectMediaURL(url string) *schema.Post {
	mediaType := media.TypeByURL(url)
	switch mediaType {
	case media.TypeVideo:
		return &schema.Post{
			Medias: []*schema.Media{
				schema.NewVideoMedia(url, ""),
			},
		}
	case media.TypeImage:
		return &schema.Post{
			Medias: []*schema.Media{
				schema.NewImageMedia(url),
			},
		}
	case media.TypeAudio:
		return &schema.Post{
			Medias: []*schema.Media{
				schema.NewAudioMedia(url, ""),
			},
		}
	}
	return nil
}

// Unshorten 通用短网址转长网址
func (s *GatewayService) Unshorten(ctx context.Context, shortURL string) string {
	isShortURL, isOverseas := sites.IsShortURLAndOverseas(shortURL)
	if !isShortURL {
		return shortURL
	}
	// 发起请求
	baseURL := node.DomesticMainNode.InternalBaseURL()
	if isOverseas {
		baseURL = node.OverseasMainNode.InternalBaseURL()
	}
	var result schema.Unshorten
	resp, err := constant.GatewayClient.R().SetContext(ctx).
		SetBody(schema.Unshorten{
			URL: shortURL,
		}).
		SetResult(&result).
		Post(fmt.Sprintf("%s/redirection/unshorten", baseURL))
	if err != nil {
		slog.Error("unshorten url failed", slog.Any("err", err))
		return shortURL
	}
	if resp.IsError() {
		slog.Error("unshorten url failed", slog.Int("status", resp.StatusCode()), slog.Any("resp", resp))
		return shortURL
	}
	return result.URL
}

// siteEnumRouter 免费/付费/PRO方案路由
func (s *GatewayService) siteEnumRouter(ctx context.Context, site *sites.Site, sourceAPP string) *sites.Site {
	sourceAPIGroup := s.gatewayRepo.GetAllSourceAPIGroup(ctx)
	// 如果请求来源在API集合中且站点是INSTAGRAM，返回INSTAGRAM_PRO
	if slices.Contains(sourceAPIGroup, sourceAPP) && site == sites.INSTAGRAM {
		return sites.INSTAGRAM_PRO
	}

	// 如果请求来源在API集合中且站点是XIAOHONGSHU，返回XIAOHONGSHU_API
	if slices.Contains(sourceAPIGroup, sourceAPP) && site == sites.XIAOHONGSHU {
		return sites.XIAOHONGSHU_API
	}

	sourceFreeGroup := s.gatewayRepo.GetAllSourceFreeGroup(ctx)

	// 如果请求来源在免费集合中，返回免费版本站点
	if slices.Contains(sourceFreeGroup, sourceAPP) {
		if siteFree, ok := sites.SiteFreeMap[site]; ok {
			return siteFree
		}
		return site
	}

	// 否则返回原站点
	return site
}
