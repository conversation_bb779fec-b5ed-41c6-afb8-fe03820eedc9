package service

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"net/http"
	"sort"
	"strings"
)

type BilibiliService struct {
	gotoService *GotoService
}

func NewBilibiliService(gotoService *GotoService) *BilibiliService {
	return &BilibiliService{
		gotoService: gotoService,
	}
}

var (
	BilibiliHeaders = map[string]string{
		constant.HeaderUserAgent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 Safari/537.36",
		constant.HeaderReferer:   "https://www.bilibili.com",
	}
	bilibiliCookies = []*http.Cookie{
		{
			Name:  "SESSDATA",
			Value: "f88d432d%2C1738291388%2C928a8%2A82CjCmXlo-8hR6g_qGvNKXcaEKzAzzn2d3eeyKZnupdXA9kf8bPml-htalwmQAnAwPO5cSVmF6eGtxb2paQURYYzVkZVh3VXI5OHp0LXltYUtKMXhmaHpDNmt6V180WVJSVjN1X2I5ek56NTBCRHFTMS1VbXFkWFM5Y0pnQzE2N24xYjgxaVdkY0p3IIEC",
		},
	}
	bilibiliClientDownloaderHeaders = map[string]string{
		constant.HeaderReferer:   "https://www.bilibili.com/",
		constant.HeaderUserAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",
	}
	supportFormatsMap = map[int64]int64{
		16:  360,
		32:  480,
		64:  720,
		74:  720,
		80:  1080,
		112: 1080,
		116: 1080,
		120: 2160,
		125: 2160,
		126: 2160,
		127: 4320,
	}
)

func GetAcgPlayInfo(ctx context.Context, bvID, cID, cover, text string) (*schema.Post, error) {
	acgAPI := fmt.Sprintf("https://api.bilibili.com/x/player/playurl?otype=json&fnver=0&fnval=3&player=2&qn=64&bvid=%s&cid=%s&platform=html5&high_quality=1",
		bvID, cID)
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(BilibiliHeaders).
		SetCookies(bilibiliCookies).
		Get(acgAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("bilibili.GetAcgPlayInfo:%s", resp.String())
	}

	result := gjson.Parse(resp.String())

	dURL := result.Get("data.durl.0")
	if !dURL.Exists() {
		return nil, fmt.Errorf("[B站]no durl info:%s", resp.String())
	}
	post := &schema.Post{
		Text: text,
	}
	sourceURL := dURL.Get("url").String()
	post.Medias = append(post.Medias, schema.NewVideoMedia(sourceURL, cover))
	return post, nil
}

func (s *BilibiliService) GetCollectionInfo(ctx context.Context, seasonID string) (*schema.Playlist, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(BilibiliHeaders).
		SetQueryParam("season_id", seasonID).
		Get("https://api.bilibili.com/x/space/fav/season/list")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("bilibili.GetCollectionInfo:%s", resp.String())
	}
	result := gjson.Parse(resp.String())
	playlist := &schema.Playlist{}
	for _, item := range result.Get("data.medias").Array() {
		url := fmt.Sprintf("https://www.bilibili.com/video/%s", item.Get("bvid").Str)
		resourceURL, err := s.gotoService.GenerateGotoURL(schema.Payload{"url": url}, false, nil)
		if err != nil {
			return nil, err
		}
		media := schema.NewVideoMedia(resourceURL, item.Get("cover").Str)
		post := &schema.Post{
			ID:         item.Get("id").Str,
			CreateTime: int(item.Get("pubtime").Int()),
			Text:       item.Get("title").Str,
		}
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}
	playlist.User = &schema.User{
		Username: result.Get("data.info.title").String(),
		Avatar:   result.Get("data.info.cover").String(),
	}
	return playlist, nil
}

func GetPgcPlayInfo(ctx context.Context, aID, bvID, cID, epID, cover, text string) (*schema.Post, error) {
	params := map[string]string{
		"avid":  aID,
		"bvID":  bvID,
		"cID":   cID,
		"ep_id": epID,
		"qn":    "127",
		"fnver": "0",
		"fnval": "2000",
		"fourk": "1",
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(BilibiliHeaders).
		SetQueryParams(params).
		SetCookies(bilibiliCookies).
		Get("https://api.bilibili.com/pgc/player/web/playurl")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("bilibili.GetPgcPlayInfo:%s", resp.String())
	}
	result := gjson.Parse(resp.String())
	if result.Get("code").String() == "-10403" {
		return nil, extract.ErrPremiumContentNotSupported
	}
	dash := result.Get("result.dash").Array()
	if len(dash) == 0 {
		if result.Get("result.durl").Exists() {
			media := schema.NewVideoMedia(result.Get("result.durl.0.url").Str, cover)
			media.Headers = bilibiliClientDownloaderHeaders
			post := &schema.Post{
				Text: text,
			}
			post.Medias = append(post.Medias, media)
			return post, err
		} else {
			return nil, fmt.Errorf("no dash info")
		}
	}
	audios := result.Get("result.dash.audio").Array()
	sort.Slice(audios, func(i, j int) bool {
		if audios[i].Get("codecs").String() != audios[j].Get("codecs").String() {
			if strings.HasSuffix(audios[i].Get("codecs").Str, "mp4a") {
				return false
			}
			return true
		}
		return audios[i].Get("size").Int() < audios[j].Get("size").Int()
	})

	audio := lo.LastOrEmpty(audios)

	videos := result.Get("result.dash.video").Array()
	sort.Slice(videos, func(i, j int) bool {
		if videos[i].Get("codecs").String() != videos[j].Get("codecs").String() {
			if strings.HasSuffix(videos[i].Get("codecs").Str, "avc") {
				return false
			}
			return true
		}
		return videos[i].Get("size").Int() < videos[j].Get("size").Int()
	})
	formatMap := make(map[int64]*schema.Format)
	for _, video := range videos {
		qualityID := video.Get("id").Int()
		quality, ok := supportFormatsMap[qualityID]
		if !ok {
			continue
		}
		if _, exists := formatMap[quality]; exists {
			continue
		}
		f := &schema.Format{
			Quality:   int(video.Get("quality").Int()),
			VideoURL:  video.Get("base_url").String(),
			VideoExt:  "mp4",
			VideoSize: int(video.Get("size").Int()),
			AudioURL:  audio.Get("base_url").String(),
			AudioExt:  "m4a",
			AudioSize: int(audio.Get("size").Int()),
			Separate:  1,
		}
		formatMap[quality] = f
	}
	// 转为切片并按 quality 倒序排序
	formats := make([]schema.Format, 0, len(formatMap))
	for _, f := range formatMap {
		formats = append(formats, *f)
	}

	sort.Slice(formats, func(i, j int) bool {
		return formats[i].Quality > formats[j].Quality
	})
	bestQualityFormat := &schema.Format{}
	found := false
	for _, f := range formats {
		if f.Separate == 0 {
			bestQualityFormat = &f
			found = true
			break
		}
	}

	if !found && len(formats) > 0 {
		bestQualityFormat = &formats[0]
	}
	media := schema.NewVideoMedia(bestQualityFormat.VideoURL, cover)
	media.Formats = formats
	media.Headers = bilibiliClientDownloaderHeaders
	post := &schema.Post{
		Text: text,
	}
	post.Medias = append(post.Medias, media)
	return post, nil
}
