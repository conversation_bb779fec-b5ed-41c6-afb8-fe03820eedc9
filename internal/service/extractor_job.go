package service

import (
	"context"
	"encoding/json"
	"errors"
	"garden/internal/repo"
	"garden/internal/schema"
	"garden/internal/service/selector"
	"garden/internal/service/selector/filter"
	"garden/pkg/node"
	"garden/pkg/rdb"
	"log/slog"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
)

const (
	networkInternal = "internal"
	networkExternal = "external"
)

const virtualExtractorRedisKey = "virtual_extractor_data"
const extractorUpdatedChannel = "extractor_updated_channel"

type ExtractorJobService struct {
	selector        *selector.Selector
	gatewayRepo     *repo.GatewayRepo
	initLock        *rdb.RedisLock
	updateCacheLock *rdb.RedisLock
}

func NewExtractorJobService(s *selector.Selector, gatewayRepo *repo.GatewayRepo) *ExtractorJobService {
	m := &ExtractorJobService{
		gatewayRepo:     gatewayRepo,
		selector:        s,
		initLock:        rdb.NewRedisLock("extractor_job_init", time.Minute*5),
		updateCacheLock: rdb.NewRedisLock("sync_site_2_extractor_dto_data_to_redis", time.Second*90),
	}
	m.init()
	go m.subscribeExtractorUpdated() // 监听提取器更新
	return m
}

type extractorCacheObj struct {
	Id              string            `json:"id,omitempty"`
	InternalAddress string            `json:"internal_address,omitempty"`
	ExternalAddress string            `json:"external_address,omitempty"`
	Name            string            `json:"name,omitempty"`
	Site            string            `json:"site,omitempty"`
	Priority        int               `json:"priority,omitempty"`
	Weight          int               `json:"weight"`
	Metadata        map[string]string `json:"metadata,omitempty"`
}

// 初始化
func (s *ExtractorJobService) init() {
	ctx := context.Background()
	_ = s.initLock.WithLock(ctx, func() error {
		var extractors []selector.Extractor
		extractors = s.getCacheExtractor(ctx) // 先从缓存中获取
		if extractors == nil {
			_ = s.updateCacheLock.WithLock(ctx, func() error { // 缓存没有再从数据数据库中获取
				extractors = s.getDBExtractor(ctx)
				s.saveExtractorCache(ctx, extractors)
				return nil
			})
		}
		s.selector.Apply(ctx, extractors) // 更新选择器内部的数据
		return nil
	})
}

// 保存至缓存
func (s *ExtractorJobService) saveExtractorCache(ctx context.Context, extractors []selector.Extractor) {
	if len(extractors) == 0 {
		return
	}
	var extractorCache = make([]extractorCacheObj, 0, len(extractors))
	for _, it := range extractors {
		item := it.(*selector.WeightedExtractor)
		extractorCache = append(extractorCache, extractorCacheObj{
			Id:              item.ID(),
			Name:            item.Name(),
			InternalAddress: item.InternalAddress(),
			ExternalAddress: item.ExternalAddress(),
			Site:            item.Site(),
			Priority:        item.Priority(),
			Weight:          item.Weight(),
			Metadata:        item.Metadata(),
		})
	}
	cacheData, _ := json.Marshal(extractorCache)
	if err := rdb.C.Set(ctx, virtualExtractorRedisKey, string(cacheData), 0).Err(); err != nil {
		slog.WarnContext(ctx, "保存提取器至缓存错误", slog.Any("err", err))
	}
	if err := rdb.C.Publish(ctx, extractorUpdatedChannel, "extractor_updated").Err(); err != nil {
		slog.WarnContext(ctx, "推送提取器更新消息错误", slog.Any("err", err))
	}
}

// 从缓存获取提取器
func (s *ExtractorJobService) getCacheExtractor(ctx context.Context) []selector.Extractor {
	data, err := rdb.C.Get(ctx, virtualExtractorRedisKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil
		}
		slog.WarnContext(ctx, "获取提取器错误", slog.Any("err", err))
		return nil
	}
	var extractorCaches []extractorCacheObj
	err = json.Unmarshal([]byte(data), &extractorCaches)
	if err != nil {
		slog.WarnContext(ctx, "反序列化提取器错误", slog.Any("err", err))
		return nil
	}
	extractors := make([]selector.Extractor, 0)
	for _, item := range extractorCaches {
		e := selector.NewExtractor(
			item.Id,
			item.InternalAddress,
			item.ExternalAddress,
			item.Name,
			item.Site,
			item.Priority,
			item.Metadata,
		)
		we := selector.NewWeightedExtractorBuilder().SetWeight(item.Weight).Build(e)
		extractors = append(extractors, we)
	}
	return extractors
}

// 从数据库中获取提取器
func (s *ExtractorJobService) getDBExtractor(ctx context.Context) []selector.Extractor {
	extractors := make([]selector.Extractor, 0)
	data := s.gatewayRepo.ListExtractors(ctx)
	for _, extractor := range data {
		for _, nodeName := range gjson.Parse(extractor.NodeNames).Array() {
			for _, siteID := range gjson.Parse(extractor.Sites).Array() {
				nodeInstance, ok := node.Nodes[nodeName.String()]
				if !ok {
					continue
				}
				extractors = append(extractors, selector.NewWeightedExtractorBuilder().
					Build(selector.NewExtractor(
						strconv.Itoa(extractor.ID),
						nodeInstance.InternalBaseURL(),
						nodeInstance.ExternalBaseURL(),
						extractor.Extractor,
						siteID.String(),
						extractor.Priority,
						map[string]string{
							"equivalent": strconv.FormatBool(extractor.Equivalent),
							"nodeName":   nodeName.String(),
						},
					)),
				)
			}
		}
	}
	return extractors
}

func (s *ExtractorJobService) updateExtractorWeight(ctx context.Context, extractors []selector.Extractor) error {
	successCounter, err := rdb.C.HGetAll(ctx, rdb.RedisExtractorSuccessCounter.Key).Result()
	if err != nil {
		return err
	}
	failCounter, err := rdb.C.HGetAll(ctx, rdb.RedisExtractorFailCounter.Key).Result()
	if err != nil {
		return err
	}
	// 更新权重
	for _, extractor := range extractors {
		we := extractor.(*selector.WeightedExtractor)
		successCount, _ := strconv.Atoi(successCounter[we.ID()])
		failCount, _ := strconv.Atoi(failCounter[we.ID()])
		we.UpdateWeight(successCount, failCount)
	}
	return nil
}

// CalculateAllExtractorWeight 计算所有提取器权重
func (s *ExtractorJobService) CalculateAllExtractorWeight(ctx context.Context) error {
	ok, err := s.updateCacheLock.TryLock(ctx) // 乐观锁
	if err != nil {
		slog.WarnContext(ctx, "获取锁错误", slog.Any("err", err))
		return err
	}
	if !ok {
		return nil
	}
	defer s.updateCacheLock.Unlock(ctx)
	if err := rdb.C.Del(ctx, virtualExtractorRedisKey).Err(); err != nil {
		slog.WarnContext(ctx, "删除缓存错误", slog.Any("err", err))
	}
	extractors := s.getDBExtractor(ctx)
	err = s.updateExtractorWeight(ctx, extractors)
	if err != nil {
		return err
	}
	s.retryFailedExtractor(ctx)
	// 保存至缓存
	s.selector.Apply(ctx, extractors)
	s.saveExtractorCache(ctx, extractors)
	m := make(map[string]struct{})
	for _, extractor := range extractors {
		if _, ok := m[extractor.ID()]; ok {
			continue
		}
		m[extractor.ID()] = struct{}{}
		slog.InfoContext(ctx, "更新提取器权重", slog.String("id", extractor.ID()), slog.Int("weight", extractor.(*selector.WeightedExtractor).Weight()))
	}
	return nil
}

// 重试失败的提取器
func (s *ExtractorJobService) retryFailedExtractor(ctx context.Context) {
	extractors, _, err := s.selector.List(ctx, selector.WithExtractorFilter(
		filter.WeightLTQFilter(0),
	))
	if err != nil {
		if errors.Is(err, selector.ErrNoAvailable) {
			slog.DebugContext(ctx, "没有失败的提取器需要复活")
		} else {
			slog.WarnContext(ctx, "获取提取器列表错误", slog.Any("err", err))
		}
		return
	}
	urlData := s.gatewayRepo.ListSiteTestURLs(ctx)
	for _, extractor := range extractors {
		we := extractor.(*selector.WeightedExtractor)
		for _, testURL := range urlData {
			for _, site := range gjson.Parse(testURL.Sites).Array() {
				if site.String() != extractor.Site() {
					continue
				}
				err := we.Invoke(ctx, "/extract/post", &schema.GatewayReq{
					URL: site.String(),
				}, nil)
				we.Pick()(ctx, selector.DoneInfo{
					Err: err,
				})
			}
		}
	}
	err = s.updateExtractorWeight(ctx, extractors)
	if err != nil {
		slog.WarnContext(ctx, "更新权重错误", slog.Any("err", err))
		return
	}
}

// 订阅提取器更新
func (s *ExtractorJobService) subscribeExtractorUpdated() {
	ctx := context.Background()
	pubsub := rdb.C.Subscribe(ctx, extractorUpdatedChannel)
	for {
		msg, err := pubsub.ReceiveMessage(ctx)
		if err != nil {
			if errors.Is(err, redis.ErrClosed) {
				continue
			}
			slog.WarnContext(ctx, "订阅错误", slog.Any("err", err))
			continue
		}
		if msg.Channel == extractorUpdatedChannel {
			slog.DebugContext(ctx, "收到更新提取器消息")
			extractors := s.getCacheExtractor(ctx) // 从redis中获取
			if extractors == nil {
				continue
			}
			s.selector.Apply(ctx, extractors) // 更新选择器内部的数据
		}
	}
}
