package config

import (
	"fmt"
	"log/slog"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config实例
var C *Config

// ConfigsDir 配置文件目录，供其他包使用
var ConfigsDir = mustFindConfigsDir()

func init() {
	env := os.Getenv("ENV")

	// 加载默认配置
	viper.SetConfigFile(filepath.Join(ConfigsDir, "config", "default.yaml"))
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("failed to read default config file: %w", err))
	}

	// 加载特定环境配置(test, prod)
	if env != "" {
		viper.SetConfigFile(filepath.Join(ConfigsDir, "config", env+".yaml"))
		err = viper.MergeInConfig()
		if err != nil {
			panic(fmt.Errorf("failed to read %s config file: %w", env, err))
		}
	}
	config := &Config{}
	err = viper.Unmarshal(config)
	if err != nil {
		panic(fmt.Errorf("failed to unmarshal config: %w", err))
	}
	config.Server.NodeName = os.Getenv("NODE_NAME")
	C = config
	slog.Info("loaded config", "env", env)
}

type Config struct {
	Server struct {
		NodeName string `mapstructure:"node_name"`
		Port     string `mapstructure:"port"`
		Mode     string `mapstructure:"mode"`
		LogLevel string `mapstructure:"log_level"`
		Cors     Cors   `mapstructure:"cors"`
	}
	Dev     bool   // 是否开发环境
	AESKey  string `mapstructure:"aes_key"`
	Swagger struct {
		Enable bool `mapstructure:"enable"`
	}
	MySQL struct {
		Host     string `mapstructure:"host"`
		Port     string `mapstructure:"port"`
		User     string `mapstructure:"user"`
		Password string `mapstructure:"password"`
		DBName   string `mapstructure:"db_name"`
	}
	Redis struct {
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		DB       int    `mapstructure:"db"`
		Password string `mapstructure:"password"`
	}
	Alert struct {
		WebhookURL          string `mapstructure:"webhook_url"`
		TemplateID          string `mapstructure:"template_id"`
		TemplateVersionName string `mapstructure:"template_version_name"`
	}
	TwitterShot struct {
		FontPath string `mapstructure:"font_path"`
		BoldFont string `mapstructure:"bold_font"`
		EmojiDir string `mapstructure:"emoji_dir"`
	} `mapstructure:"twitter_shot"`
}

type Cors struct {
	AllowOrigins     []string `mapstructure:"allow_origins"`
	AllowHeaders     []string `mapstructure:"allow_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
}

// mustFindConfigsDir 从当前目录开始向上查找 configs 文件夹
func mustFindConfigsDir() string {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		panic(fmt.Errorf("failed to get current directory: %w", err))
	}

	// 从当前目录开始向上查找
	for {
		configsPath := filepath.Join(currentDir, "configs")
		if _, err := os.Stat(configsPath); err == nil {
			return configsPath
		}

		// 获取父目录
		parentDir := filepath.Dir(currentDir)
		// 如果已经到达根目录，说明没找到
		if parentDir == currentDir {
			panic(fmt.Errorf("configs directory not found"))
		}
		currentDir = parentDir
	}
}
