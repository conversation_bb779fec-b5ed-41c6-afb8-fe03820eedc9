// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSiteTestURL = "site_test_url"

// SiteTestURL 站点测试URL
type SiteTestURL struct {
	ID    int    `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"id"` // 自增ID
	Sites string `gorm:"column:sites;not null;comment:站点名称列表" json:"sites"`              // 站点名称列表
	Urls  string `gorm:"column:urls;not null;comment:测试URL列表" json:"urls"`               // 测试URL列表
}

// TableName SiteTestURL's table name
func (*SiteTestURL) TableName() string {
	return TableNameSiteTestURL
}
