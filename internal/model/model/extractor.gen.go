// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameExtractor = "extractor"

// Extractor 提取器
type Extractor struct {
	ID         int    `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"id"`                                                             // 自增ID
	Extractor  string `gorm:"column:extractor;not null;comment:提取器名称" json:"extractor"`                                                                   // 提取器名称
	Priority   int    `gorm:"column:priority;not null;default:1;comment:优先级，数值越小优先级越高" json:"priority"`                                                   // 优先级，数值越小优先级越高
	Equivalent bool   `gorm:"column:equivalent;not null;default:1;comment:不同节点的是否视为能力一致 0不一致(当该提取器失败了，可尝试其他节点) 1一致(当该提取器失败了，不再尝试其他节点)" json:"equivalent"` // 不同节点的是否视为能力一致 0不一致(当该提取器失败了，可尝试其他节点) 1一致(当该提取器失败了，不再尝试其他节点)
	NodeNames  string `gorm:"column:node_names;not null;comment:支持的节点名称列表" json:"node_names"`                                                             // 支持的节点名称列表
	Sites      string `gorm:"column:sites;not null;comment:支持的站点名称列表" json:"sites"`                                                                       // 支持的站点名称列表
}

// TableName Extractor's table name
func (*Extractor) TableName() string {
	return TableNameExtractor
}
