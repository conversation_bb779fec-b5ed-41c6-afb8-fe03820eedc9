// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"garden/internal/model/model"
)

func newExtractor(db *gorm.DB, opts ...gen.DOOption) extractor {
	_extractor := extractor{}

	_extractor.extractorDo.UseDB(db, opts...)
	_extractor.extractorDo.UseModel(&model.Extractor{})

	tableName := _extractor.extractorDo.TableName()
	_extractor.ALL = field.NewAsterisk(tableName)
	_extractor.ID = field.NewInt(tableName, "id")
	_extractor.Extractor = field.NewString(tableName, "extractor")
	_extractor.Priority = field.NewInt(tableName, "priority")
	_extractor.Equivalent = field.NewBool(tableName, "equivalent")
	_extractor.NodeNames = field.NewString(tableName, "node_names")
	_extractor.Sites = field.NewString(tableName, "sites")

	_extractor.fillFieldMap()

	return _extractor
}

// extractor 提取器
type extractor struct {
	extractorDo

	ALL        field.Asterisk
	ID         field.Int    // 自增ID
	Extractor  field.String // 提取器名称
	Priority   field.Int    // 优先级，数值越小优先级越高
	Equivalent field.Bool   // 不同节点的是否视为能力一致 0不一致(当该提取器失败了，可尝试其他节点) 1一致(当该提取器失败了，不再尝试其他节点)
	NodeNames  field.String // 支持的节点名称列表
	Sites      field.String // 支持的站点名称列表

	fieldMap map[string]field.Expr
}

func (e extractor) Table(newTableName string) *extractor {
	e.extractorDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e extractor) As(alias string) *extractor {
	e.extractorDo.DO = *(e.extractorDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *extractor) updateTableName(table string) *extractor {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt(table, "id")
	e.Extractor = field.NewString(table, "extractor")
	e.Priority = field.NewInt(table, "priority")
	e.Equivalent = field.NewBool(table, "equivalent")
	e.NodeNames = field.NewString(table, "node_names")
	e.Sites = field.NewString(table, "sites")

	e.fillFieldMap()

	return e
}

func (e *extractor) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *extractor) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 6)
	e.fieldMap["id"] = e.ID
	e.fieldMap["extractor"] = e.Extractor
	e.fieldMap["priority"] = e.Priority
	e.fieldMap["equivalent"] = e.Equivalent
	e.fieldMap["node_names"] = e.NodeNames
	e.fieldMap["sites"] = e.Sites
}

func (e extractor) clone(db *gorm.DB) extractor {
	e.extractorDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e extractor) replaceDB(db *gorm.DB) extractor {
	e.extractorDo.ReplaceDB(db)
	return e
}

type extractorDo struct{ gen.DO }

type IExtractorDo interface {
	gen.SubQuery
	Debug() IExtractorDo
	WithContext(ctx context.Context) IExtractorDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IExtractorDo
	WriteDB() IExtractorDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IExtractorDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IExtractorDo
	Not(conds ...gen.Condition) IExtractorDo
	Or(conds ...gen.Condition) IExtractorDo
	Select(conds ...field.Expr) IExtractorDo
	Where(conds ...gen.Condition) IExtractorDo
	Order(conds ...field.Expr) IExtractorDo
	Distinct(cols ...field.Expr) IExtractorDo
	Omit(cols ...field.Expr) IExtractorDo
	Join(table schema.Tabler, on ...field.Expr) IExtractorDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IExtractorDo
	RightJoin(table schema.Tabler, on ...field.Expr) IExtractorDo
	Group(cols ...field.Expr) IExtractorDo
	Having(conds ...gen.Condition) IExtractorDo
	Limit(limit int) IExtractorDo
	Offset(offset int) IExtractorDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IExtractorDo
	Unscoped() IExtractorDo
	Create(values ...*model.Extractor) error
	CreateInBatches(values []*model.Extractor, batchSize int) error
	Save(values ...*model.Extractor) error
	First() (*model.Extractor, error)
	Take() (*model.Extractor, error)
	Last() (*model.Extractor, error)
	Find() ([]*model.Extractor, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Extractor, err error)
	FindInBatches(result *[]*model.Extractor, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Extractor) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IExtractorDo
	Assign(attrs ...field.AssignExpr) IExtractorDo
	Joins(fields ...field.RelationField) IExtractorDo
	Preload(fields ...field.RelationField) IExtractorDo
	FirstOrInit() (*model.Extractor, error)
	FirstOrCreate() (*model.Extractor, error)
	FindByPage(offset int, limit int) (result []*model.Extractor, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IExtractorDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e extractorDo) Debug() IExtractorDo {
	return e.withDO(e.DO.Debug())
}

func (e extractorDo) WithContext(ctx context.Context) IExtractorDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e extractorDo) ReadDB() IExtractorDo {
	return e.Clauses(dbresolver.Read)
}

func (e extractorDo) WriteDB() IExtractorDo {
	return e.Clauses(dbresolver.Write)
}

func (e extractorDo) Session(config *gorm.Session) IExtractorDo {
	return e.withDO(e.DO.Session(config))
}

func (e extractorDo) Clauses(conds ...clause.Expression) IExtractorDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e extractorDo) Returning(value interface{}, columns ...string) IExtractorDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e extractorDo) Not(conds ...gen.Condition) IExtractorDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e extractorDo) Or(conds ...gen.Condition) IExtractorDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e extractorDo) Select(conds ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e extractorDo) Where(conds ...gen.Condition) IExtractorDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e extractorDo) Order(conds ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e extractorDo) Distinct(cols ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e extractorDo) Omit(cols ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e extractorDo) Join(table schema.Tabler, on ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e extractorDo) LeftJoin(table schema.Tabler, on ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e extractorDo) RightJoin(table schema.Tabler, on ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e extractorDo) Group(cols ...field.Expr) IExtractorDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e extractorDo) Having(conds ...gen.Condition) IExtractorDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e extractorDo) Limit(limit int) IExtractorDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e extractorDo) Offset(offset int) IExtractorDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e extractorDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IExtractorDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e extractorDo) Unscoped() IExtractorDo {
	return e.withDO(e.DO.Unscoped())
}

func (e extractorDo) Create(values ...*model.Extractor) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e extractorDo) CreateInBatches(values []*model.Extractor, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e extractorDo) Save(values ...*model.Extractor) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e extractorDo) First() (*model.Extractor, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Extractor), nil
	}
}

func (e extractorDo) Take() (*model.Extractor, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Extractor), nil
	}
}

func (e extractorDo) Last() (*model.Extractor, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Extractor), nil
	}
}

func (e extractorDo) Find() ([]*model.Extractor, error) {
	result, err := e.DO.Find()
	return result.([]*model.Extractor), err
}

func (e extractorDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Extractor, err error) {
	buf := make([]*model.Extractor, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e extractorDo) FindInBatches(result *[]*model.Extractor, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e extractorDo) Attrs(attrs ...field.AssignExpr) IExtractorDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e extractorDo) Assign(attrs ...field.AssignExpr) IExtractorDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e extractorDo) Joins(fields ...field.RelationField) IExtractorDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e extractorDo) Preload(fields ...field.RelationField) IExtractorDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e extractorDo) FirstOrInit() (*model.Extractor, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Extractor), nil
	}
}

func (e extractorDo) FirstOrCreate() (*model.Extractor, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Extractor), nil
	}
}

func (e extractorDo) FindByPage(offset int, limit int) (result []*model.Extractor, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e extractorDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e extractorDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e extractorDo) Delete(models ...*model.Extractor) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *extractorDo) withDO(do gen.Dao) *extractorDo {
	e.DO = *do.(*gen.DO)
	return e
}
