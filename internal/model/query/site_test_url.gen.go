// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"garden/internal/model/model"
)

func newSiteTestURL(db *gorm.DB, opts ...gen.DOOption) siteTestURL {
	_siteTestURL := siteTestURL{}

	_siteTestURL.siteTestURLDo.UseDB(db, opts...)
	_siteTestURL.siteTestURLDo.UseModel(&model.SiteTestURL{})

	tableName := _siteTestURL.siteTestURLDo.TableName()
	_siteTestURL.ALL = field.NewAsterisk(tableName)
	_siteTestURL.ID = field.NewInt(tableName, "id")
	_siteTestURL.Sites = field.NewString(tableName, "sites")
	_siteTestURL.Urls = field.NewString(tableName, "urls")

	_siteTestURL.fillFieldMap()

	return _siteTestURL
}

// siteTestURL 站点测试URL
type siteTestURL struct {
	siteTestURLDo

	ALL   field.Asterisk
	ID    field.Int    // 自增ID
	Sites field.String // 站点名称列表
	Urls  field.String // 测试URL列表

	fieldMap map[string]field.Expr
}

func (s siteTestURL) Table(newTableName string) *siteTestURL {
	s.siteTestURLDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s siteTestURL) As(alias string) *siteTestURL {
	s.siteTestURLDo.DO = *(s.siteTestURLDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *siteTestURL) updateTableName(table string) *siteTestURL {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt(table, "id")
	s.Sites = field.NewString(table, "sites")
	s.Urls = field.NewString(table, "urls")

	s.fillFieldMap()

	return s
}

func (s *siteTestURL) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *siteTestURL) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 3)
	s.fieldMap["id"] = s.ID
	s.fieldMap["sites"] = s.Sites
	s.fieldMap["urls"] = s.Urls
}

func (s siteTestURL) clone(db *gorm.DB) siteTestURL {
	s.siteTestURLDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s siteTestURL) replaceDB(db *gorm.DB) siteTestURL {
	s.siteTestURLDo.ReplaceDB(db)
	return s
}

type siteTestURLDo struct{ gen.DO }

type ISiteTestURLDo interface {
	gen.SubQuery
	Debug() ISiteTestURLDo
	WithContext(ctx context.Context) ISiteTestURLDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISiteTestURLDo
	WriteDB() ISiteTestURLDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISiteTestURLDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISiteTestURLDo
	Not(conds ...gen.Condition) ISiteTestURLDo
	Or(conds ...gen.Condition) ISiteTestURLDo
	Select(conds ...field.Expr) ISiteTestURLDo
	Where(conds ...gen.Condition) ISiteTestURLDo
	Order(conds ...field.Expr) ISiteTestURLDo
	Distinct(cols ...field.Expr) ISiteTestURLDo
	Omit(cols ...field.Expr) ISiteTestURLDo
	Join(table schema.Tabler, on ...field.Expr) ISiteTestURLDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISiteTestURLDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISiteTestURLDo
	Group(cols ...field.Expr) ISiteTestURLDo
	Having(conds ...gen.Condition) ISiteTestURLDo
	Limit(limit int) ISiteTestURLDo
	Offset(offset int) ISiteTestURLDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISiteTestURLDo
	Unscoped() ISiteTestURLDo
	Create(values ...*model.SiteTestURL) error
	CreateInBatches(values []*model.SiteTestURL, batchSize int) error
	Save(values ...*model.SiteTestURL) error
	First() (*model.SiteTestURL, error)
	Take() (*model.SiteTestURL, error)
	Last() (*model.SiteTestURL, error)
	Find() ([]*model.SiteTestURL, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SiteTestURL, err error)
	FindInBatches(result *[]*model.SiteTestURL, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SiteTestURL) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISiteTestURLDo
	Assign(attrs ...field.AssignExpr) ISiteTestURLDo
	Joins(fields ...field.RelationField) ISiteTestURLDo
	Preload(fields ...field.RelationField) ISiteTestURLDo
	FirstOrInit() (*model.SiteTestURL, error)
	FirstOrCreate() (*model.SiteTestURL, error)
	FindByPage(offset int, limit int) (result []*model.SiteTestURL, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISiteTestURLDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s siteTestURLDo) Debug() ISiteTestURLDo {
	return s.withDO(s.DO.Debug())
}

func (s siteTestURLDo) WithContext(ctx context.Context) ISiteTestURLDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s siteTestURLDo) ReadDB() ISiteTestURLDo {
	return s.Clauses(dbresolver.Read)
}

func (s siteTestURLDo) WriteDB() ISiteTestURLDo {
	return s.Clauses(dbresolver.Write)
}

func (s siteTestURLDo) Session(config *gorm.Session) ISiteTestURLDo {
	return s.withDO(s.DO.Session(config))
}

func (s siteTestURLDo) Clauses(conds ...clause.Expression) ISiteTestURLDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s siteTestURLDo) Returning(value interface{}, columns ...string) ISiteTestURLDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s siteTestURLDo) Not(conds ...gen.Condition) ISiteTestURLDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s siteTestURLDo) Or(conds ...gen.Condition) ISiteTestURLDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s siteTestURLDo) Select(conds ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s siteTestURLDo) Where(conds ...gen.Condition) ISiteTestURLDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s siteTestURLDo) Order(conds ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s siteTestURLDo) Distinct(cols ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s siteTestURLDo) Omit(cols ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s siteTestURLDo) Join(table schema.Tabler, on ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s siteTestURLDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s siteTestURLDo) RightJoin(table schema.Tabler, on ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s siteTestURLDo) Group(cols ...field.Expr) ISiteTestURLDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s siteTestURLDo) Having(conds ...gen.Condition) ISiteTestURLDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s siteTestURLDo) Limit(limit int) ISiteTestURLDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s siteTestURLDo) Offset(offset int) ISiteTestURLDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s siteTestURLDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISiteTestURLDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s siteTestURLDo) Unscoped() ISiteTestURLDo {
	return s.withDO(s.DO.Unscoped())
}

func (s siteTestURLDo) Create(values ...*model.SiteTestURL) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s siteTestURLDo) CreateInBatches(values []*model.SiteTestURL, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s siteTestURLDo) Save(values ...*model.SiteTestURL) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s siteTestURLDo) First() (*model.SiteTestURL, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SiteTestURL), nil
	}
}

func (s siteTestURLDo) Take() (*model.SiteTestURL, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SiteTestURL), nil
	}
}

func (s siteTestURLDo) Last() (*model.SiteTestURL, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SiteTestURL), nil
	}
}

func (s siteTestURLDo) Find() ([]*model.SiteTestURL, error) {
	result, err := s.DO.Find()
	return result.([]*model.SiteTestURL), err
}

func (s siteTestURLDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SiteTestURL, err error) {
	buf := make([]*model.SiteTestURL, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s siteTestURLDo) FindInBatches(result *[]*model.SiteTestURL, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s siteTestURLDo) Attrs(attrs ...field.AssignExpr) ISiteTestURLDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s siteTestURLDo) Assign(attrs ...field.AssignExpr) ISiteTestURLDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s siteTestURLDo) Joins(fields ...field.RelationField) ISiteTestURLDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s siteTestURLDo) Preload(fields ...field.RelationField) ISiteTestURLDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s siteTestURLDo) FirstOrInit() (*model.SiteTestURL, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SiteTestURL), nil
	}
}

func (s siteTestURLDo) FirstOrCreate() (*model.SiteTestURL, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SiteTestURL), nil
	}
}

func (s siteTestURLDo) FindByPage(offset int, limit int) (result []*model.SiteTestURL, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s siteTestURLDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s siteTestURLDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s siteTestURLDo) Delete(models ...*model.SiteTestURL) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *siteTestURLDo) withDO(do gen.Dao) *siteTestURLDo {
	s.DO = *do.(*gen.DO)
	return s
}
