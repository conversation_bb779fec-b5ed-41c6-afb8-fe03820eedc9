package constant

import (
	"crypto/tls"
	"garden/internal/config"
	"garden/pkg/useragent"
	"time"

	"resty.dev/v3"
)

// MaxRetryCount 提取最大重试次数
const MaxRetryCount = 2

const (
	HeaderOrigin        = "Origin" // 来源站点
	HeaderRequestSource = "Source" // 请求来源

	HeaderUserAgent   = "User-Agent"   // UserAgent
	HeaderReferer     = "Referer"      // Referer
	HeaderContentType = "Content-Type" // Content-Type
	HeaderCookie      = "Cookie"       // Cookie
)

const (
	ContextIp            = "ip"            // 客户端IP
	ContextUserAgent     = "userAgent"     // 客户端UA
	ContextSiteDomain    = "siteDomain"    // 当前站点域名
	ContextRequestSource = "requestSource" // 请求来源
)

// RestyClient 请求客户端
var RestyClient = resty.NewWithTransportSettings(&resty.TransportSettings{
	DialerTimeout: 3 * time.Second,
}).SetTimeout(15*time.Second).
	SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
	SetHeader(HeaderUserAgent, useragent.Desktop()).
	SetDebug(config.C.Dev)

// RestyClientNoRedirect 不进行重定向的请求客户端
var RestyClientNoRedirect = resty.NewWithTransportSettings(&resty.TransportSettings{
	DialerTimeout: 3 * time.Second,
}).SetTimeout(15*time.Second).
	SetRedirectPolicy(resty.NoRedirectPolicy()).
	SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
	SetHeader(HeaderUserAgent, useragent.Desktop()).
	SetDebug(config.C.Dev)

// 美国住宅代理
const usResidentialProxy = "http://brd-customer-hl_b4a38f16-zone-residential_proxy:<EMAIL>:22225"

// RestyResidentialProxyClient 美国住宅代理请求客户端
var RestyResidentialProxyClient = resty.NewWithTransportSettings(&resty.TransportSettings{
	DialerTimeout: 3 * time.Second,
}).SetTimeout(15*time.Second).
	SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
	SetHeader(HeaderUserAgent, useragent.Desktop()).
	SetDebug(config.C.Dev).
	SetProxy(usResidentialProxy)

// RestyResidentialProxyClientNoRedirect 美国住宅代理不进行重定向的请求客户端
var RestyResidentialProxyClientNoRedirect = resty.NewWithTransportSettings(&resty.TransportSettings{
	DialerTimeout: 3 * time.Second,
}).SetTimeout(15*time.Second).
	SetRedirectPolicy(resty.NoRedirectPolicy()).
	SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
	SetHeader(HeaderUserAgent, useragent.Desktop()).
	SetDebug(config.C.Dev).
	SetProxy(usResidentialProxy)

// GatewayClient 网关请求客户端
var GatewayClient = resty.NewWithTransportSettings(&resty.TransportSettings{
	DialerTimeout: 3 * time.Second,
}).SetTimeout(10*time.Second).
	SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
	SetHeader(HeaderUserAgent, "Gateway")

// payload类型
const (
	PayloadSinglePostPayload        = "SinglePostPayload"
	PayloadBilibiliAcgPayload       = "BilibiliAcgPayload"
	PayloadBilibiliBangumiPayload   = "BilibiliBangumiPayload"
	PayloadTwitterScreenshotPayload = "TwitterScreenshotPayload"
	PayloadStreamPayload            = "StreamPayload"
)
