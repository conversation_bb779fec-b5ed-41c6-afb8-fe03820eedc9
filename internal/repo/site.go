package repo

import (
	"context"
	"github.com/gongyinshi/shared/sites"
	"slices"
)

var iiiLabWebSupportedSites = []*sites.Site{
	sites.DOUYIN,
	sites.TIKTOK,
	sites.TOUTIAO,
	sites.WEIBO,
	sites.BILIBILI,
	sites.ACFUN,
	sites.YOUTUBE,
	sites.TWITTER,
	sites.INSTAGRAM,
	sites.VIMEO,
	sites.FACEBOOK,
	sites.TUMBLR,
	sites.ZUIYOU,
	sites.WEISHI,
	sites.KG,
	sites.QUANMIN,
	sites.MOMO,
	sites.MEIPAI,
	sites.YUNYINYUE,
	sites.QUDUOPAI,
	sites.INKE,
	sites.XIAOYING,
	sites.PEARVIDEO,
}

// 免费版SnapAny网站支持的站点
var snapanyWebSupportedSites = []*sites.Site{
	sites.DOUYIN,
	sites.TIKTOK,
	sites.TOUTIAO,
	sites.BILIBILI,
	sites.VK,
	sites.PINTEREST,
	sites.TRILLER,
	sites.REDDIT,
	sites.DAILYMOTION,
	sites.LEMON8,
	sites.KWAI,
	sites.LIKEE,
	sites.VIMEO,
	sites.XVIDEOS,
	sites.TUMBLR,
	sites.SUNO,
	sites.YT_DLP_OVERSEAS,
}

type SiteRepo struct {
}

func NewSiteRepo() *SiteRepo {
	return &SiteRepo{}
}

func (r *SiteRepo) GetSiteByURL(ctx context.Context, url string) *sites.Site {
	site := sites.GetByURL(url)
	if site == nil {
		site = sites.YT_DLP_OVERSEAS
	}
	return site
}

func (r *SiteRepo) IsIIIlabWebSupportedSite(ctx context.Context, site *sites.Site) bool {
	return slices.Contains(iiiLabWebSupportedSites, site)
}

func (r *SiteRepo) IsSnapanyWebSupportedSite(ctx context.Context, site *sites.Site) bool {
	return slices.Contains(snapanyWebSupportedSites, site)
}
