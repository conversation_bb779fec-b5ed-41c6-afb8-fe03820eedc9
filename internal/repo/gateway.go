package repo

import (
	"context"
	"encoding/json"
	"garden/internal/model/model"
	"garden/internal/schema"
	"garden/pkg/db"

	"gorm.io/gorm"
)

type GatewayRepo struct {
}

func NewGatewayRepo() *GatewayRepo {
	return &GatewayRepo{}
}

func (r *GatewayRepo) ListExtractors(ctx context.Context) []*model.Extractor {
	result, err := db.Q.Extractor.WithContext(ctx).Find()
	if err != nil {
		panic(err)
	}
	return result
}

func (r *GatewayRepo) UpdateExtractors(ctx context.Context, extractors []*schema.UpdateExtractorReqItem) error {
	// 开启事务，保证原子性
	return db.Q.Extractor.UnderlyingDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取所有现有的extractor记录
		var allExtractors []*model.Extractor
		if err := tx.Find(&allExtractors).Error; err != nil {
			return err
		}

		// 创建现有记录的ID映射，方便快速查找
		existingExtractorMap := make(map[int]*model.Extractor)
		for _, extractor := range allExtractors {
			existingExtractorMap[extractor.ID] = extractor
		}

		// 创建请求中ID的集合，方便查找
		requestIDMap := make(map[int]bool)
		for _, item := range extractors {
			requestIDMap[item.ID] = true
		}

		// 处理请求中的数据（创建或更新）
		for _, item := range extractors {
			nodeNamesJson, err := json.Marshal(item.NodeNames)
			if err != nil {
				return err
			}
			sitesJson, err := json.Marshal(item.Sites)
			if err != nil {
				return err
			}

			extractorModel := &model.Extractor{
				ID:         item.ID,
				Extractor:  item.Extractor,
				Priority:   item.Priority,
				Equivalent: item.Equivalent,
				NodeNames:  string(nodeNamesJson),
				Sites:      string(sitesJson),
			}

			// 通过映射判断记录是否存在
			if existingExtractor, exists := existingExtractorMap[item.ID]; exists {
				// 记录存在，更新记录
				if err := tx.Model(existingExtractor).Updates(extractorModel).Error; err != nil {
					return err
				}
			} else {
				// 记录不存在，创建新记录
				if err := tx.Create(extractorModel).Error; err != nil {
					return err
				}
			}
		}

		// 处理不在请求中的记录，清空sites和node_names字段
		for _, extractor := range allExtractors {
			if !requestIDMap[extractor.ID] {
				// 清空sites和node_names字段
				emptySliceJson := "[]"
				if err := tx.Model(extractor).Updates(map[string]interface{}{
					"sites":      emptySliceJson,
					"node_names": emptySliceJson,
				}).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

func (r *GatewayRepo) ListSiteTestURLs(ctx context.Context) []*model.SiteTestURL {
	result, err := db.Q.SiteTestURL.WithContext(ctx).Find()
	if err != nil {
		panic(err)
	}
	return result
}

func (r *GatewayRepo) UpdateSiteTestURLs(ctx context.Context, siteTestURLs []*schema.UpdateSiteTestURLReqItem) error {
	// 开启事务，保证原子性
	return db.Q.SiteTestURL.UnderlyingDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 清空表
		if err := tx.Exec("DELETE FROM site_test_url").Error; err != nil {
			return err
		}

		// 2. 构造批量插入数据
		var models []*model.SiteTestURL
		for _, item := range siteTestURLs {
			sitesJson, err := json.Marshal(item.Sites)
			if err != nil {
				return err
			}
			urlsJson, err := json.Marshal(item.URLs)
			if err != nil {
				return err
			}
			models = append(models, &model.SiteTestURL{
				Sites: string(sitesJson),
				Urls:  string(urlsJson),
			})
		}
		if len(models) > 0 {
			if err := tx.Create(&models).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

const (
	SourceSnapanyWeb     = "snapany_web"
	SourceIIILabWeb      = "iiilab_web"
	SourceHengHengMaoAPI = "henghengmao_api"
	SourceMediaPlatform  = "mediaplatform" // 公众号
	SourceUnknown        = "unknown"
)

func (r *GatewayRepo) GetAllSourceAPIGroup(ctx context.Context) []string {
	return []string{SourceHengHengMaoAPI}
}

func (r *GatewayRepo) GetAllSourceFreeGroup(ctx context.Context) []string {
	return []string{SourceSnapanyWeb, SourceIIILabWeb, SourceMediaPlatform}
}
