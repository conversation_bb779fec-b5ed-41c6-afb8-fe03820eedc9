package schema

type UpdateExtractorReqItem struct {
	ID         int      `json:"id"`
	Extractor  string   `json:"extractor"`
	Priority   int      `json:"priority"`
	Equivalent bool     `json:"equivalent"`
	NodeNames  []string `json:"node_names"`
	Sites      []string `json:"sites"`
}

type ListExtractorRecordsRespItem struct {
	ID         int      `json:"id"`
	Extractor  string   `json:"extractor"`
	Priority   int      `json:"priority"`
	Equivalent bool     `json:"equivalent"`
	NodeNames  []string `json:"node_names"`
	Sites      []string `json:"sites"`
}

type UpdateSiteTestURLReqItem struct {
	Sites []string `json:"sites"`
	URLs  []string `json:"urls"`
}

type UpdateSiteTestURLRespItem struct {
	ID    int      `json:"id"`
	Sites []string `json:"sites"`
	URLs  []string `json:"urls"`
}
