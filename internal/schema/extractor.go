package schema

import (
	"garden/pkg/media"
	"github.com/gongyinshi/shared/urlx"
)

type ExtractReq struct {
	URL       string `json:"url" binding:"required" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 网址
	Extractor string `json:"extractor" binding:"required" example:"youtube"`                               // 提取器
	Cursor    string `json:"cursor" example:"123"`                                                         // 游标(用于播放列表的分页)
	Source    string `json:"source" example:"henghengmao"`                                                 // 请求来源的应用/产品
}

type Post struct {
	Text       string   `json:"text" example:"文案"`                   // 文案
	Medias     []*Media `json:"medias"`                              // 媒体列表
	Overseas   int      `json:"overseas" example:"0"`                // 是否海外资源 0: 否 1: 是
	Stats      *Stats   `json:"stats,omitempty"`                     // 统计信息
	ID         string   `json:"id,omitempty" example:"123"`          // 帖子ID
	CreateTime int      `json:"create_time,omitempty" example:"123"` // 创建时间
}

// IsEmpty 判断post的medias是否为空
func (p *Post) IsEmpty() bool {
	return len(p.Medias) == 0
}

type Media struct {
	MediaType       media.Type        `json:"media_type" example:"video、image、audio"`                                            // 媒体类型
	ResourceURL     string            `json:"resource_url" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"`                // 资源地址URL
	PreviewURL      string            `json:"preview_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"`       // 封面URL
	PreviewProxyURL string            `json:"preview_proxy_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 封面代理URL
	Formats         []Format          `json:"formats,omitempty"`                                                                 // 格式
	Headers         map[string]string `json:"headers,omitempty"`                                                                 // 请求头
}

func NewVideoMedia(resourceURL, previewURL string) *Media {
	return &Media{
		MediaType:   media.TypeVideo,
		ResourceURL: urlx.SanitizeHTTPURL(resourceURL),
		PreviewURL:  urlx.SanitizeHTTPURL(previewURL),
	}
}

func NewAudioMedia(resourceURL, previewURL string) *Media {
	return &Media{
		MediaType:   media.TypeAudio,
		ResourceURL: urlx.SanitizeHTTPURL(resourceURL),
		PreviewURL:  urlx.SanitizeHTTPURL(previewURL),
	}
}

func NewImageMedia(resourceURL string) *Media {
	return &Media{
		MediaType:   media.TypeImage,
		ResourceURL: urlx.SanitizeHTTPURL(resourceURL),
	}
}

type Format struct {
	Quality      int    `json:"quality,omitempty" example:"1080"`                                          // 质量
	VideoURL     string `json:"video_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 视频URL
	VideoExt     string `json:"video_ext,omitempty" example:"mp4"`                                         // 视频扩展名
	VideoSize    int    `json:"video_size,omitempty" example:"1024"`                                       // 视频大小
	AudioURL     string `json:"audio_url,omitempty" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 音频URL
	AudioExt     string `json:"audio_ext,omitempty" example:"mp3"`                                         // 音频扩展名
	AudioSize    int    `json:"audio_size,omitempty" example:"1024"`                                       // 音频大小
	Separate     int    `json:"separate" example:"0"`                                                      // 是否音视频分离 0: 否 1: 是
	QualityNote  string `json:"quality_note,omitempty" example:"4K/2K/1080P/720P/480P"`                    // 质量，如4K/2K/1080P/720P/480P
	AlternateURL string `json:"alternate_url,omitempty" example:"https://xxxx"`                            // 备用URL
	Language     string `json:"language,omitempty" example:"en"`                                           // 语言
}

type Stats struct {
	CommentCount *int `json:"comment_count" example:"100"` // 评论数
	DiggCount    *int `json:"digg_count" example:"100"`    // 点赞数
	PlayCount    *int `json:"play_count" example:"100"`    // 播放数
	ShareCount   *int `json:"share_count" example:"100"`   // 分享数
}

type Playlist struct {
	NextCursor string  `json:"next_cursor" example:"no_more"` // 游标
	HasMore    bool    `json:"has_more" example:"false"`      // 是否还有更多
	Posts      []*Post `json:"posts"`                         // 帖子列表
	Overseas   int     `json:"overseas" example:"0"`          // 是否海外资源 0: 否 1: 是
	User       *User   `json:"user,omitempty"`                // 用户信息
}

type User struct {
	Username string `json:"username" example:"jack"`                             // 用户名
	Avatar   string `json:"avatar" example:"https://www.example.com/avatar.png"` // 头像
}
