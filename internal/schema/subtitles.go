package schema

import "time"

type SubtitlesResp struct {
	ID           string      `json:"id" example:"H14bBuluwB8"` // 帖子ID
	Text         string      `json:"text"`                     // 视频标题(文案)
	Description  string      `json:"description"`              // 描述(可能没有)
	Duration     *int        `json:"duration"`                 // 时长(秒)
	PublishedAt  *time.Time  `json:"published_at"`             // 发布时间
	ThumbnailURL string      `json:"thumbnail_url"`            // 缩略图(视频封面地址)  返回尺寸最大的图
	Subtitles    []*Subtitle `json:"subtitles"`                // 字幕列表
}

type Subtitle struct {
	LanguageName string         `json:"language_name"` // 语言名称
	LanguageCode string         `json:"language_code"` // 语言代码
	URLs         []*SubtitleURL `json:"urls"`          // 字幕URL
}

type SubtitleURL struct {
	URL    string `json:"url"`    // 字幕URL
	Format string `json:"format"` // 格式 按顺序如：srt、vtt、ttml、json3、srv1、srv2、srv3
}
