package schema

type GatewayReq struct {
	URL    string `json:"url" binding:"required" example:"https://www.youtube.com/watch?v=dQw4w9WgXcQ"` // 网址
	Cursor string `json:"cursor" example:"123"`                                                         // 游标(用于播放列表的分页)
	Source string `json:"source"`                                                                       // 请求来源
}

type GatewayResp struct{}
