package schema

import "github.com/spf13/cast"

type GotoReq struct {
	Payload        string `json:"payload" form:"payload"`
	D              bool   `json:"d" form:"d"`                           // payload内是否包含直链
	J              bool   `json:"j" form:"j"`                           // 返回数据为json格式，不是403重定向
	YoutubeVideoId string `json:"youtubeVideoId" form:"youtubeVideoId"` // youtube视频id
}

type GotoResp struct {
	Redirect bool
	Post     *Post
	URL      string
}

type Payload map[string]any

func (p Payload) Get(key string) any {
	return p[key]
}

func (p Payload) Set(key string, value any) {
	p[key] = value
}

func (p Payload) GetExpireTime() int64 {
	return cast.ToInt64(p.Get("expire_time"))
}

func (p Payload) SetExpireTime(expireTime int64) {
	p.Set("expire_time", expireTime)
}

func (p Payload) GetURL() string {
	return cast.ToString(p.Get("url"))
}

func (p Payload) SetURL(url string) {
	p.Set("url", url)
}

func (p Payload) GetType() string {
	return cast.ToString(p.Get("type"))
}

func (p Payload) SetType(t string) {
	p.Set("type", t)
}

func (p Payload) GetHeaders() map[string]string {
	return cast.ToStringMapString(p.Get("headers"))
}

func (p Payload) SetHeaders(headers map[string]string) {
	p.Set("headers", headers)
}

type StreamReq struct {
	Payload string `json:"payload" form:"payload"`
}
