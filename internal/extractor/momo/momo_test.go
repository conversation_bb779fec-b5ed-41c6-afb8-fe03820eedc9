package momo

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://m.immomo.com/s/moment/new-share-v2/ap7164036754.html?time=1552130574&name=0oft4uoDv74sqZNgUldibzThORoAZoznKTN3mmsZ95g=&avatar=8DDFC84B-36D8-271C-F22D-50BF0A7C40BE20190307&isdaren=0&isuploader=1&from=weibo
// https://m.immomo.com/s/moment/new-share-v2.html?momentids=am6205713339
// https://m.immomo.com/s/moment/new-share-v2.html?momentids=al5520406242
// https://m.immomo.com/s/moment/new-share-v2.html?momentids=ak5321251586&from=timeline
// https://m.immomo.com/s/moment/new-share-v2/ax10417756467.html?time=1644851885&name=T6yyaTel65BA6vQP0llVMA==&avatar=150631E5-B9F3-0815-8F31-9D33B436B15C20170729&isdaren=0&isuploader=0&from=qqfriend
// https://m.immomo.com/s/moment/new-share-v2/ar8429508258.html?time=1644852003&name=T6yyaTel65BA6vQP0llVMA==&avatar=150631E5-B9F3-0815-8F31-9D33B436B15C20170729&isdaren=1&isuploader=0&from=qqfriend
// 不支持直播间视频
// https://live-api.immomo.com/h5/v2/share/home/<USER>
// https://web.immomo.com/live/28232326
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://m.immomo.com/s/moment/new-share-v2/ar8429508258.html?time=1644852003&name=T6yyaTel65BA6vQP0llVMA",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
