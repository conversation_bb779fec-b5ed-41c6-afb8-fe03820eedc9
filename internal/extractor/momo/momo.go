package momo

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"net/url"
	"regexp"
	"strings"
)

var regex = regexp.MustCompile(`\/([a-zA-Z]{2}\d+)\.htm`)

const api = "https://m.immomo.com/inc/microvideo/share/profiles"

type result struct {
	Data struct {
		List []struct {
			Video struct {
				VideoURL string `json:"video_url"`
				Cover    struct {
					LargeURL string `json:"l"`
				} `json:"cover"`
				DecoratorTexts string `json:"decorator_texts"`
			} `json:"video"`
		} `json:"list"`
	} `json:"data"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	if strings.Contains(req.URL, "/live") {
		return nil, extract.ErrLiveStreamNotSupported
	}
	u, err := url.Parse(req.URL)
	if err != nil {
		return nil, err
	}
	var feedID string
	matches := regex.FindStringSubmatch(u.Path)
	if len(matches) > 1 {
		feedID = matches[1]
	} else {
		feedID = u.Query().Get("momentids")
	}
	if feedID == "" {
		return nil, fmt.Errorf("momo: url中未找到feed_id")
	}

	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetFormData(map[string]string{"feedids": feedID}).
		SetHeader("User-Agent", useragent.RandomMobile()).
		SetResult(&result).
		Post(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("momo: %s", resp.String())
	}
	if len(result.Data.List) == 0 {
		return nil, fmt.Errorf("momo: 未找到视频")
	}
	data := result.Data.List[0].Video
	return &schema.Post{
		Text: data.DecoratorTexts,
		Medias: []*schema.Media{
			schema.NewVideoMedia(data.VideoURL, data.Cover.LargeURL),
		},
	}, nil
}
