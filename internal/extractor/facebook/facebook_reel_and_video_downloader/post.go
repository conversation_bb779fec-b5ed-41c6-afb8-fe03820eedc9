package facebook_reel_and_video_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"garden/pkg/useragent"
	"net/url"
	"strings"

	"github.com/samber/lo"
)

// https://rapidapi.com/vikas5914/api/facebook-reel-and-video-downloader/playground/apiendpoint_a9e8b919-ab57-4e1a-be16-1a3137ce7a96
const (
	postAPI = "https://facebook-reel-and-video-downloader.p.rapidapi.com/app/main.php"
)

var headers = map[string]string{
	"x-rapidapi-host": "facebook-reel-and-video-downloader.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = resolveShareLink(ctx, req.URL)
	if err != nil {
		return
	}
	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("url", req.URL).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("facebook_reel_and_video_downloader: %s", resp.String())
	}
	if !result.Success {
		return nil, fmt.Errorf("facebook_reel_and_video_downloader: %s", result.Title)
	}

	post = &schema.Post{
		Text: result.Title,
	}

	videoURL := lo.CoalesceOrEmpty(result.Links.DownloadHighQuality, result.Links.DownloadLowQuality)
	if videoURL != "" {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, result.Thumbnail))
	}

	for _, media := range result.Media {
		if strings.ToLower(media.Type) == "photo" {
			post.Medias = append(post.Medias, schema.NewImageMedia(media.Image))
		} else {
			videoURL := lo.CoalesceOrEmpty(media.HdURL, media.SdURL)
			if videoURL != "" {
				post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, media.Image))
			}
		}
	}

	return post, nil
}

// resolveShareLink 将分享链接解析为普通链接
// https://www.facebook.com/share/r/ZVwpReDtnCUe4x7o/?mibextid=WC7FNe
func resolveShareLink(ctx context.Context, originalURL string) (string, error) {
	u, err := url.Parse(originalURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %v", err)
	}

	// 检查是否是分享链接
	if !strings.HasPrefix(u.Path, "/share/") {
		return originalURL, nil
	}

	finalURL := redirect.ResolveFirstRedirectURL(ctx, originalURL, &redirect.Options{
		UserAgent:   useragent.Desktop(),
		RestyClient: constant.RestyResidentialProxyClientNoRedirect,
	})

	return finalURL, nil
}
