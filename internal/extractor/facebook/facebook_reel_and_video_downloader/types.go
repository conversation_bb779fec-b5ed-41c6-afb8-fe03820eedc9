package facebook_reel_and_video_downloader

type postResult struct {
	Success   bool   `json:"success"`
	Title     string `json:"title"`
	Thumbnail string `json:"thumbnail"`
	URL       string `json:"url"`
	Links     struct {
		DownloadHighQuality string `json:"Download High Quality"`
		DownloadLowQuality  string `json:"Download Low Quality"`
	} `json:"links"`
	Media []struct {
		ID    string `json:"id"`
		Type  string `json:"type"`
		Image string `json:"image"`
		SdURL string `json:"sd_url"`
		HdURL string `json:"hd_url"`
	} `json:"media"`
}
