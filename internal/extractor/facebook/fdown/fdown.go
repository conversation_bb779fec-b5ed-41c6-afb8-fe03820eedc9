package fdown

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"

	"github.com/PuerkitoBio/goquery"
	"github.com/samber/lo"
)

var headers = map[string]string{
	"accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
	"accept-language":           "zh,zh-TW;q=0.9,zh-HK;q=0.8,ja;q=0.7,es-CO;q=0.6,es;q=0.5,zh-CN;q=0.4",
	"cache-control":             "max-age=0",
	"content-type":              "application/x-www-form-urlencoded",
	"origin":                    "https://fdown.net",
	"priority":                  "u=0, i",
	"referer":                   "https://fdown.net/index.php",
	"sec-ch-ua":                 "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
	"sec-ch-ua-mobile":          "?0",
	"sec-ch-ua-platform":        "\"macOS\"",
	"sec-fetch-dest":            "document",
	"sec-fetch-mode":            "navigate",
	"sec-fetch-site":            "same-origin",
	"sec-fetch-user":            "?1",
	"upgrade-insecure-requests": "1",
	"user-agent":                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
	"cookie":                    "_ga=GA1.1.134117859.1744275778; FCNEC=%5B%5B%22AKsRol-MxA7S-lfLYOhBzQh3xxL31xXpRo1ywudnLFMB7PxXvWyMBeTm_rxp3k7Zc-HoV-YxDzPjpmg1VRFa6UCbYLNqXJPaNYE5t4sZeD0qWhARgD9ueY8NKLa3TRRELzOGn_-fN2RKlMotU-nkP640bmv8eXe5iQ%3D%3D%22%5D%5D; _lr_env_src_ats=false; _cc_id=edbec3d43497612693993d8332a960c3; panoramaId_expiry=1746883863672; panoramaId=ed39555b6212ef22a2380e1377e1a9fb927a27ea8c4492ee565b61cc15dfc108; panoramaIdType=panoDevice; __qca=P1-c470e2c7-3889-4d6e-90f5-1cf8ccf92b09; __gads=ID=3a70177fd6502baa:T=1744275777:RT=1746802130:S=ALNI_MayUUMq1dtpMKRcQKhzdNeZfaoJyw; __gpi=UID=00001097ce3980a3:T=1744275777:RT=1746802130:S=ALNI_MbX0TlQvtnFFhDxE42CxBejT9JAHQ; __eoi=ID=c056a0faa1f3a489:T=1744275777:RT=1746802130:S=AA-AfjaBDZXGf8AXK-bBblHZ7KqP; cf_clearance=ca6M7fkeC1QKaA96TqgxnmvS34zTFUaVDsVt.4NT7qw-1746802134-1.2.1.1-dgfPuCCblhFWwkl2jx5pa2BqGekKv4YOGb_Ce0u17d9iUnFHFNx7tUq1g7rEoDKIL_bbRWhQcjFIomArJ6x1yXiHm4QhM1p2jp97lWWxYcs41a23OKqDwAYZFwyux10.9ya59X7.Egwu99GaaPSvzl6Gl8hHhpI5C2CSdzM8k0NpmCpG9LoMAxQ.1gN2VL9gTaWgICGJVcaTn4t.Tlbf5FXzZcmAdLZ0CTgxX0XQfgd3hqkYP4lNWQUL_0WC2W2fnwDnW3g.TR9C0fhe8294mbwPnIV4k80gCEMUmQdX4vybmeEuXjOn1rblcOw15Snb6mE1hzvN679Hh5LkMtZryL5MnxeYgM.7YhqMUPv6f6A; _ga_82ERN9JZD3=GS2.1.s1746797459$o2$g1$t1746802179$j9$l0$h0",
}

func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetFormData(map[string]string{
			"URLz": req.URL,
		}).
		Post("https://fdown.net/download.php")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("fdown: %s", resp.String())
	}
	// Load the HTML document
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	videoURL := lo.CoalesceOrEmpty(doc.Find("#hdlink").AttrOr("href", ""), doc.Find("#sdlink").AttrOr("href", ""))
	if videoURL == "" {
		return nil, fmt.Errorf("fdown: no video URL found")
	}

	coverURL := doc.Find("#result > div.col-xs-6.col-xs-offset-3.no-padding.lib-item > div > div > div:nth-child(1) > img").First().AttrOr("src", "")

	return &schema.Post{
		Medias: []*schema.Media{schema.NewVideoMedia(videoURL, coverURL)},
	}, nil
}
