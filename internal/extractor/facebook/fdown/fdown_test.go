package fdown

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://www.facebook.com/ThePatternTraderTraining/videos/839336177247688
// https://www.facebook.com/m.ltn.tw/videos/1229608734251179
// https://fb.watch/gvfMc6g6Pl/
// https://www.facebook.com/share/r/ZVwpReDtnCUe4x7o/?mibextid=WC7FNe
func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://fb.watch/gvfMc6g6Pl/",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
