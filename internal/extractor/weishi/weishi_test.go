package weishi

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://h5.qzone.qq.com/weishi/feed/CLK9BD4pPpOCdRto/wsfeed?_proxy=1&_wv=1&id=CLK9BD4pPpOCdRto
// https://h5.weishi.qq.com/weishi/feed/71GLvDUEC1GaNxtwA/wsfeed?_proxy=1&_wv=1&id=71GLvDUEC1GaNxtwA&spid=1539248643789479
// https://isee.weishi.qq.com/ws/app-pages/share/index.html?wxplay=1&id=7jF9LOMVT1K9pR8DE
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://isee.weishi.qq.com/ws/app-pages/share/index.html?wxplay=1&feedid=7jF9LOMVT1R8DE",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
