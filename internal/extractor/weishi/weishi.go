package weishi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/useragent"
	"net/url"
	"regexp"

	"github.com/samber/lo"
)

var pathFeedIDRegex = regexp.MustCompile(`/feed/([a-zA-Z0-9]+)`)

const api = "https://h5.weishi.qq.com/webapp/json/weishi/WSH5GetPlayPage"

type result struct {
	Data struct {
		Feeds []struct {
			VideoURL string `json:"video_url"`
			FeedDesc string `json:"feed_desc"`
			Images   []struct {
				URL string `json:"url"`
			} `json:"images"`
		} `json:"feeds"`
	} `json:"data"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	u, err := url.Parse(req.URL)
	if err != nil {
		return nil, err
	}
	var feedID string
	matches := pathFeedIDRegex.FindStringSubmatch(u.Path)
	if len(matches) > 1 {
		feedID = matches[1]
	} else {
		feedID = lo.CoalesceOrEmpty(u.Query().Get("id"), u.Query().Get("feedid"))
	}
	if feedID == "" {
		return nil, fmt.Errorf("weishi: url中未找到feed_id")
	}
	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader("User-Agent", useragent.RandomDesktop()).
		SetHeader("Referer", req.URL).
		SetQueryParam("feedid", feedID).
		SetResult(&result).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("weishi: %s", resp.String())
	}
	if len(result.Data.Feeds) == 0 {
		return nil, fmt.Errorf("weishi: 未找到视频")
	}
	data := result.Data.Feeds[0]
	return &schema.Post{
		Text: data.FeedDesc,
		Medias: []*schema.Media{
			schema.NewVideoMedia(data.VideoURL, lo.FirstOrEmpty(data.Images).URL),
		},
	}, nil
}
