package yunyinyue

import (
	"context"
	"garden/internal/schema"
	"testing"
)

/*
WEB: http://music.163.com/#/video?id=2F2D3F14D17B83D91573E6AB5C0CADC3 失效链接
MV: http://music.163.com/mv/838?userid=122414339
好友动态: http://music.163.com/event?id=2413617421&uid=448451092&userid=122414339
mlog:
https://st.music.163.com/mlog/mlog.html?id=a1rmoGwQwcrkwg&type=2&userid=417026720&songId=1336482717&startTime=0 // 网页不存咋
https://st.music.163.com/mlog/mlog.html?id=a1o9P51Klh51m0A&type=2&userid=6443641557&songId=null&startTime=null // 无法访问
https://st.music.163.com/mlog/mlog.html?id=a13rWP81HOXDAP&type=2&userid=6443641557&songId=1896550554&startTime=0 // 无法访问
https://st.music.163.com/mlog/mlog.html?id=a1GpVZJ2Ph18yr9&type=2&userid=6443641557&songId=null&startTime=null // 无法访问

https://fn.music.163.com/g/mlog/mlog-mobile/landing/mv?app_version=8.7.65&id=5323557&userid=394134&dlt=0846 // 无法访问

歌曲不支持：http://music.163.com/program/1367944538/104601777?userid=122414339
*/
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://music.163.com/#/program/1367944538/104601777?userid=122414339",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
