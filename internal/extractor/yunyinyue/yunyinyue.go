package yunyinyue

import (
	"context"
	"encoding/json"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/useragent"
	"github.com/PuerkitoBio/goquery"
	"github.com/tidwall/gjson"
	"net/url"
	"strings"
)

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	if strings.Contains(req.URL, "/#/") {
		req.URL = strings.Replace(req.URL, "#/", "", 1)
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderUserAgent, useragent.RandomDesktop()).
		Get(req.URL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("yunyinyue post: %s", resp.String())
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
	if err != nil {
		return nil, err
	}
	u, err := url.Parse(req.URL)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	flashBox := doc.Find("#flash_box")
	if flashBox.Length() == 0 {
		// 可能是好友动态链接
		if strings.Contains(u.Path, "event") {
			eventData := doc.Find("#event-data")
			if eventData.Length() > 0 {
				eventText := strings.TrimSpace(eventData.Text())
				var eventJSON map[string]interface{}
				if err := json.Unmarshal([]byte(eventText), &eventJSON); err == nil {
					if jsonStr, ok := eventJSON["json"].(string); ok {
						videoID := gjson.Get(jsonStr, "video.videoId").String()
						if videoID != "" {
							newReq := &schema.ExtractReq{
								URL: fmt.Sprintf("http://music.163.com/video/%s", videoID),
							}
							return Extract(ctx, newReq)
						}
					}
				}
			}
		}
		return nil, fmt.Errorf("html中没有flash_box")
	}

	flashvars, exists := flashBox.Attr("data-flashvars")
	if !exists {
		return nil, fmt.Errorf("html中没有视频信息")
	}

	queryDict, err := url.ParseQuery(flashvars)
	if err != nil {
		return nil, fmt.Errorf("解析flashvars失败: %w", err)
	}

	if len(queryDict["murl"]) == 0 {
		return nil, fmt.Errorf("html中没有视频信息")
	}

	var videoURL, coverURL, text string

	// 获取视频地址，优先使用高清地址
	if len(queryDict["hurl"]) > 0 && queryDict["hurl"][0] != "" {
		videoURL = queryDict["hurl"][0]
	} else {
		videoURL = queryDict["murl"][0]
	}

	// 获取封面地址
	if len(queryDict["coverImg"]) > 0 {
		coverURL = queryDict["coverImg"][0]
	}

	// 获取标题
	if len(queryDict["trackName"]) > 0 {
		text = queryDict["trackName"][0]
	}

	return &schema.Post{
		Text: text,
		Medias: []*schema.Media{
			schema.NewVideoMedia(videoURL, coverURL),
		},
	}, nil
}
