package xiaohongshu

import (
	"context"
	"garden/internal/schema"
	"testing"
)

const (
	imageURL  = "https://www.xiaohongshu.com/explore/68457fab0000000022028c87?xsec_token=ABVK_hXr7xbYPR9e9N8nltePUP4tBDhULasf53YjqKTdc=&xsec_source=pc_feed"
	imagesURL = "https://www.xiaohongshu.com/explore/685b587d000000002203cada?xsec_token=ABJpSErD2I2PZi9ENJkp7EpftLO9iIbLmOotPvmvzEZeE=&xsec_source=pc_feed"
	videoURL  = "https://www.xiaohongshu.com/explore/686079bf0000000012017d98?xsec_token=ABeauib3UwfFSp-10kcGrc3f5M3OrUdUwdWphjAf6N8to=&xsec_source=pc_feed"
	// 提取器可能不支持live类型
	liveURL = "https://www.xiaohongshu.com/discovery/item/6716064f000000001b012ac4?source=webshare&xhsshare=pc_web&xsec_token=ABiXgFEyeAkdmacb3KwOvPQCN-vRTVTdNxJ_iq5pX_Q6w=&xsec_source=pc_share"
)

func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: liveURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
