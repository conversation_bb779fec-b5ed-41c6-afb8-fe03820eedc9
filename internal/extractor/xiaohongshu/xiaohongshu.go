package xiaohongshu

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"github.com/PuerkitoBio/goquery"
	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"strings"

	"regexp"
	"time"
)

var (
	jsRegexp = regexp.MustCompile(`window\.__INITIAL_STATE__.*?=.*?`)
	headers  = map[string]string{
		"Host":       "www.xiaohongshu.com",
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36",
	}
)

func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	// 重定向URL
	req.URL = redirect.ResolveFinalURL(ctx, req.URL, &redirect.Options{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
	})
	resp, err := constant.RestyClient.R().
		SetContext(ctx).
		SetHeaders(headers).
		//SetDoNotParseResponse(true). // 如果你不需要读取 body，可加快性能
		Get(req.URL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("xiaohongshu: %s", resp.String())
	}
	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	// 查找包含window.__INITIAL_STATE__的script标签
	var initialState string
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		text := s.Text()
		if strings.Contains(text, "window.__INITIAL_STATE__") {
			initialState = text
			return
		}
	})

	if initialState == "" {
		return nil, fmt.Errorf("未找到初始状态数据")
	}

	// 处理JavaScript代码，提取JSON数据
	initialState = jsRegexp.ReplaceAllString(initialState, "")
	initialState = strings.ReplaceAll(initialState, "\n", "\\n")
	initialState = strings.ReplaceAll(initialState, "\r", "\\r")
	initialState = strings.ReplaceAll(initialState, "undefined", "null")

	// 解析JSON
	result := gjson.Parse(initialState)
	post = parseResult(result)
	return post, nil
}

func parseResult(result gjson.Result) *schema.Post {
	post := &schema.Post{
		Medias: nil,
	}
	note := result.Get("note")
	post.ID = note.Get("firstNoteId").String()
	noteDetailMap := note.Get("noteDetailMap").Map()
	noteMap := noteDetailMap[post.ID]

	note = noteMap.Get("note")
	title := note.Get("title").String()
	desc := note.Get("desc").String()
	post.Text = lo.CoalesceOrEmpty(desc, title)

	var urlDefault string
	imageList := note.Get("imageList").Array()
	if len(imageList) > 0 {
		for _, image := range imageList {
			urlDefault = image.Get("urlDefault").String()
			post.Medias = append(post.Medias, schema.NewImageMedia(urlDefault))
			if image.Get("livePhoto").Bool() {
				streams := image.Get("stream").Map()
				for _, stream := range streams {
					if len(stream.Array()) != 0 {
						for _, s := range stream.Array() {
							post.Medias = append(post.Medias, schema.NewVideoMedia(s.Get("masterUrl").String(), urlDefault))
						}
					}
				}
			}
		}
	}

	mediaStreams := note.Get("video.media.stream").Map()
	for _, stream := range mediaStreams {
		if len(stream.Array()) != 0 {
			for _, m := range stream.Array() {
				post.Medias = append(post.Medias, schema.NewVideoMedia(m.Get("masterUrl").String(), urlDefault))
			}
		}
	}

	post.CreateTime = int(note.Get("time").Float() / 1000)

	return post
}

// GetFinalURL 获取最终URL，处理短链接重定向
func getFinalURL(ctx context.Context, req *schema.ExtractReq) (string, error) {
	xhslinkRe := regexp.MustCompile(`xhslink`)
	xiaohongshiRe := regexp.MustCompile(`xiaohongshu`)

	if xhslinkRe.MatchString(req.URL) {
		headers := map[string]string{
			"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
			"Accept-Encoding":           "gzip, deflate",
			"Accept-Language":           "zh-CN,zh;q=0.9",
			"Cache-Control":             "no-cache",
			"Connection":                "keep-alive",
			"Host":                      "xhslink.com",
			"Pragma":                    "no-cache",
			"Upgrade-Insecure-Requests": "1",
			"User-Agent":                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
		}

		resp, err := constant.RestyClient.R().
			SetContext(ctx).
			SetHeaders(headers).
			SetQueryParam("url", req.URL). // 将原始短链放入查询参数
			SetTimeout(30 * time.Second).
			SetDoNotParseResponse(true). // 不读取 body，提高效率
			Get(req.URL)

		if err != nil {
			return "", err
		}

		if resp.IsError() {
			return "", fmt.Errorf("tiktok_best_experience: %s", resp.String())
		}

		// 获取重定向的Location
		location := resp.Header().Get("Location")
		if location != "" {
			req.URL = location
		} else {
			return "", fmt.Errorf("无法获取重定向URL")
		}
	} else if xiaohongshiRe.MatchString(req.URL) {
		return req.URL, nil
	} else {
		return "", fmt.Errorf("不支持的URL格式")
	}

	return "", nil
}
