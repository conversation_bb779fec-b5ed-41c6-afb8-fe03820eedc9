package fastest_social_video_and_image_downloader

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://www.pinterest.com/pin/69454019244113613/
// https://pin.it/CrJJdsW
// https://www.pinterest.com/pin/3870349671953041/
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.pinterest.com/pin/69454019244113613/",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
