package fastest_social_video_and_image_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
)

const postAPI = "https://fastest-social-video-and-image-downloader.p.rapidapi.com/pinterest"

var headers = map[string]string{
	"x-rapidapi-host": "fastest-social-video-and-image-downloader.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

// https://rapidapi.com/vikas5914/api/fastest-social-video-and-image-downloader/playground/apiendpoint_fbebddf7-6ba6-4e9a-9178-4c0a45f1e5eb
func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("url", req.URL).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] fastest-social-video-and-image-downloader: %s", resp.String())
	}
	if !result.Success || result.Data.URL == "" {
		return nil, fmt.Errorf("[post] fastest-social-video-and-image-downloader: invalid result")
	}
	post = &schema.Post{}
	post.Medias = make([]*schema.Media, 0)
	if result.Type == "video" {
		post.Medias = append(post.Medias, schema.NewVideoMedia(result.Data.URL, result.Data.Thumbnail))
	} else {
		post.Medias = append(post.Medias, schema.NewImageMedia(result.Data.URL))
	}
	return post, nil
}
