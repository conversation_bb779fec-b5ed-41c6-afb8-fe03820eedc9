package instagram_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/extractor/instagram/flashapi1"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

const (
	postAPI = "https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index"
)

var headers = map[string]string{
	"x-rapidapi-host": "instagram-downloader-download-instagram-videos-stories.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return
	}

	paths := urlx.ParsePath(req.URL)
	if paths[0] == "stories" {
		return flashapi1.Extract(ctx, req)
	}
	if paths[1] == "reel" || paths[1] == "tv" {
		req.URL = strings.Replace(req.URL, "/"+paths[1]+"/", "/p/", 1)
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("url", req.URL).
		SetTimeout(30 * time.Second).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("tiktok_best_experience: %s", resp.String())
	}

	result := gjson.Parse(resp.String())
	post = &schema.Post{
		Text: result.Get("title").String(),
	}

	postMedia := result.Array()
	for _, m := range postMedia {
		mediaType := m.Get("type").String()
		if mediaType == "Video" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(m.Get("media").String(), m.Get("thumbnail").String()))
		} else {
			post.Medias = append(post.Medias, schema.NewImageMedia(m.Get("media").String()))
		}
	}
	return post, nil
}
