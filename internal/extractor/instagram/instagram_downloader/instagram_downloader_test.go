package instagram_downloader

import (
	"context"

	"garden/internal/schema"
	"testing"
)

// https://www.instagram.com/p/CkLuA-MvBtS/?igshid=YmMyMTA2M2Y=
// https://www.instagram.com/reel/DJW7V8ygwk1/
// https://www.instagram.com/instagram/p/DJSzzMUsdHF/?img_index=1
// Reel: https://www.instagram.com/instagram/reel/DJE6HG4A7cs/

func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.instagram.com/instagram/tv/DJE6HG4A7cs/",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
