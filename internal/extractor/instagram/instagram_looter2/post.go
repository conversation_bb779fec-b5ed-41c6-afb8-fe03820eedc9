package instagram_looter2

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/extractor/instagram/flashapi1"
	"garden/internal/extractor/instagram/instagram_bulk_profile_scrapper"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

const postAPI = "https://instagram-looter2.p.rapidapi.com/post-dl"

var (
	headers = map[string]string{
		"x-rapidapi-key":  "**************************************************",
		"x-rapidapi-host": "instagram-looter2.p.rapidapi.com",
	}
)

// 单个帖子信息：https://rapidapi.com/iq.faceok/api/instagram-looter2/playground/apiendpoint_8aff225d-ac59-429e-80b1-a208e62e25d3
func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "stories" {
		return instagram_bulk_profile_scrapper.Extract(ctx, req)
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetTimeout(20*time.Second).
		SetQueryParam("url", req.URL).
		SetHeaders(headers).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] instagram_looter2: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	if !result.Get("status").Exists() || !result.Get("data").Exists() {
		if strings.Contains(result.Get("errorMessage").Str, "This post does not exist") {
			return flashapi1.Extract(ctx, req)
		}
		return nil, fmt.Errorf("[post] instagram_looter2: %s", result.String())
	}
	commentCount := int(result.Get("data.comment_count").Int())
	diggCount := int(result.Get("data.like_count").Int())

	post = &schema.Post{
		Text: result.Get("data.caption").String(),
		Stats: &schema.Stats{
			CommentCount: &commentCount,
			DiggCount:    &diggCount,
		},
	}
	for _, r := range result.Get("data.medias").Array() {
		if r.Get("type").Str == "video" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(r.Get("link").Str, r.Get("img").Str))
		} else {
			post.Medias = append(post.Medias, schema.NewImageMedia(r.Get("link").Str))
		}
	}
	return post, nil
}
