package instagram_looter2

import (
	"context"
	"encoding/json"
	"garden/internal/schema"
	"testing"
)

// https://www.instagram.com/p/CkLuA-MvBtS/?igshid=YmMyMTA2M2Y=
// https://www.instagram.com/reel/DJW7V8ygwk1/
// https://www.instagram.com/instagram/p/DJSzzMUsdHF/?img_index=1
// Reel: https://www.instagram.com/instagram/reel/DJE6HG4A7cs/
// 快拍精选: https://www.instagram.com/stories/highlights/18029499352961095/
// 快拍: https://www.instagram.com/stories/instagram/
// TODO 短链接
// https://www.instagram.com/s/aGlnaGxpZ2h0OjE4MTcyNjgyMjI3MTE4NDEx
// https://www.instagram.com/share/BACxeUl-2i
// https://www.instagram.com/s/aGlnaGxpZ2h0OjE4MTcyNjgyMjI3MTE4NDEx
func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.instagram.com/instagram/reel/DJE6HG4A7cs/",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}

// https://www.instagram.com/instagram/
// https://www.instagram.com/nike/
// https://www.instagram.com/explore/tags/heart/
// https://www.instagram.com/explore/tags/muwomen/
func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://www.instagram.com/explore/tags/heart/",
	})
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(playlist, "", "  ")
	t.Log(string(b))
}
