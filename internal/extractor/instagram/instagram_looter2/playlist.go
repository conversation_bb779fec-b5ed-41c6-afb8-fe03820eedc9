package instagram_looter2

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/tidwall/gjson"
	"resty.dev/v3"
)

const (
	tagAPI  = "https://instagram-looter2.p.rapidapi.com/tag-feeds"
	userAPI = "https://instagram-looter2.p.rapidapi.com/user-feeds2"
)

// 用户主页： https://rapidapi.com/iq.faceok/api/instagram-looter2/playground/apiendpoint_4029f30e-bf12-499d-8dc0-2c97a7dc47c3
// HashTag列表： https://rapidapi.com/iq.faceok/api/instagram-looter2/playground/apiendpoint_67049f8f-6992-44a7-8bef-dd6bfa67e073
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	playlist = &schema.Playlist{}
	paths := urlx.ParsePath(req.URL)

	var resp *resty.Response
	if paths[0] == "explore" && paths[1] == "tags" {
		params := map[string]string{
			"query": paths[2],
		}
		if req.Cursor != "" {
			params["nextMaxId"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetTimeout(30 * time.Second).
			SetQueryParams(params).
			Get(tagAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram_bulk_profile_scrapper.tag: %s", resp.String())
		}
		result := gjson.Parse(resp.String())
		if result.Get("status").Str != "ok" || !result.Get("data").Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		timeline := result.Get("data.hashtag.edge_hashtag_to_media")
		items := timeline.Get("edges").Array()
		if !timeline.Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		playlist = &schema.Playlist{}
		if timeline.Get("page_info.has_next_page").Exists() {
			playlist.HasMore = true
			playlist.NextCursor = timeline.Get("page_info.end_cursor").String()
		}
		if req.Cursor == "" {
			edges := result.Get("data.hashtag.edge_hashtag_to_top_posts.edges")
			if edges.Exists() {
				items = append(items, edges.Array()...)
			}
		}
		for _, item := range items {
			i := item.Get("node")
			if !i.Exists() {
				continue
			}
			text := i.Get("edge_media_to_caption.edges.0.node.text").Str
			post := &schema.Post{
				ID:         i.Get("id").Str,
				CreateTime: int(i.Get("taken_at_timestamp").Int()),
				Text:       text,
			}
			post.Medias = append(post.Medias, schema.NewImageMedia(i.Get("display_url").Str))
			playlist.Posts = append(playlist.Posts, post)
		}
		playlist.User = &schema.User{
			Username: paths[2],
		}
	} else {
		userInfo, err := instagram.GetUserInfoByURL(ctx, req.URL, false)
		if err != nil {
			return nil, err
		}
		params := map[string]string{
			"id":    userInfo.UID,
			"count": "30",
		}
		if req.Cursor != "" {
			params["end_cursor"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetTimeout(30 * time.Second).
			SetQueryParams(params).
			Get(userAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram_bulk_profile_scrapper.uid: %s", resp.String())
		}
		result := gjson.Parse(resp.String())
		if result.Get("status").Str != "ok" || !result.Get("data.user").Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		timeline := result.Get("data.user.edge_owner_to_timeline_media")
		if !timeline.Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		pageInfo := timeline.Get("page_info")
		items := timeline.Get("edges").Array()

		if pageInfo.Get("has_next_page").Exists() {
			playlist.HasMore = true
			playlist.NextCursor = pageInfo.Get("end_cursor").String()
		}
		for _, item := range items {
			i := item.Get("node")
			if !i.Exists() {
				continue
			}
			text := i.Get("edge_media_to_caption.edges.0.node.text").Str
			post := &schema.Post{
				ID:         i.Get("id").Str,
				CreateTime: int(i.Get("taken_at_timestamp").Int()),
				Text:       text,
			}
			medias, err := instagram.GetMediaListV2(i)
			if err != nil {
				return nil, err
			}
			post.Medias = append(post.Medias, medias...)
			playlist.Posts = append(playlist.Posts, post)
		}
		if len(items) != 0 {
			playlist.User = &schema.User{
				Username: items[0].Get("node.owner.username").Str,
			}
		}
	}
	return playlist, nil
}
