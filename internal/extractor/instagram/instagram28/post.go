package instagram28

import (
	"context"
	"errors"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"

	"github.com/tidwall/gjson"
)

const (
	storiesAPI   = "https://instagram28.p.rapidapi.com/stories"
	shortCodeAPI = "https://instagram28.p.rapidapi.com/media_info"
)

var (
	headers = map[string]string{
		"x-rapidapi-host": "instagram28.p.rapidapi.com",
		"x-rapidapi-key":  "**************************************************",
	}
)

// 单个帖子：https://rapidapi.com/yuananf/api/instagram28/playground/apiendpoint_f51ff5a7-d9f2-4434-ac86-6d6ad9eaede9
// 快拍： https://rapidapi.com/yuananf/api/instagram28/playground/apiendpoint_6079b070-6b45-4d4e-835d-32de4dca8a9b
// 支持post/stories 不支持highlights
func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "stories" {
		if paths[1] == "highlights" {
			return nil, extract.ErrRetryable
		}
		return getStoryByUserName(ctx, paths[1])
	}

	return getPostByShortCode(ctx, instagram.GetShortCodeByURL(req.URL))
}

func getPostByShortCode(ctx context.Context, shortCode string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("short_code", shortCode).
		Get(shortCodeAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] flashapi: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	p := result.Get("data.shortcode_media")
	if !p.Exists() {
		return nil, errors.New(resp.String())
	}
	textEdges := p.Get("edge_media_to_caption.edges").Array()
	text := textEdges[0].Get("node.text").String()
	commentCount := int(p.Get("edge_media_preview_comment.count").Int())
	likeCount := int(p.Get("edge_media_preview_like.count").Int())
	post := &schema.Post{
		Text: text,
		Stats: &schema.Stats{
			CommentCount: &commentCount,
			DiggCount:    &likeCount,
		},
	}
	medias, err := instagram.GetMediaListV2(p)
	if err != nil {
		return nil, err
	}
	post.Medias = append(post.Medias, medias...)
	return post, nil
}

func getStoryByUserName(ctx context.Context, userName string) (post *schema.Post, err error) {
	userID, err := instagram.GetUserIDByUsername(ctx, userName)
	if err != nil {
		return nil, err
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("user_id", userID).
		Get(storiesAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] instagram28: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	stories := result.Get("reel.items")
	if !stories.Exists() {
		return nil, extract.ErrNoStory
	}
	post = &schema.Post{
		Text: userName + "的快拍",
	}

	for _, story := range stories.Array() {
		medias, err := instagram.GetMediaList(story)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}
	return post, nil
}
