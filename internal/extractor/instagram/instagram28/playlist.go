package instagram28

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/tidwall/gjson"
	"resty.dev/v3"
)

var (
	hashtagAPI  = "https://instagram28.p.rapidapi.com/hash_tag_medias"
	playlistAPI = "https://instagram28.p.rapidapi.com/medias"
)

// 用户帖子： https://rapidapi.com/yuananf/api/instagram28/playground/apiendpoint_98d05d19-e64a-411d-8ed5-413627593bb5
// HashTag帖子： https://rapidapi.com/yuananf/api/instagram28/playground/apiendpoint_c3a270af-aa64-4b2d-9c0d-fe62cd530349
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	playlist = &schema.Playlist{}
	paths := urlx.ParsePath(req.URL)
	var userNameFlag bool
	var resp *resty.Response
	if paths[0] == "explore" && paths[1] == "tags" {
		userNameFlag = false
		params := map[string]string{
			"hash_tag": paths[2],
		}
		if req.Cursor != "" {
			params["next_cursor"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetTimeout(15 * time.Second).
			SetQueryParams(params).
			Get(hashtagAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram28.tag: %s", resp.String())
		}
	} else {
		userNameFlag = true
		userInfo, err := instagram.GetUserInfoByURL(ctx, req.URL, false)
		if err != nil {
			return nil, err
		}
		params := map[string]string{
			"user_id":    userInfo.UID,
			"batch_size": "50",
		}
		if req.Cursor != "" {
			params["next_cursor"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetTimeout(15 * time.Second).
			SetQueryParams(params).
			Get(playlistAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram28.uid: %s", resp.String())
		}
	}
	result := gjson.Parse(resp.String())
	if len(result.Array()) == 0 {
		return nil, extract.ErrRetryable
	}
	var timelineMedia gjson.Result
	if userNameFlag {
		timelineMedia = result.Get("data.user.edge_owner_to_timeline_media")
	} else {
		timelineMedia = result.Get("data.hashtag.edge_hashtag_to_media")
	}
	if !timelineMedia.Exists() {
		return nil, extract.ErrRetryable
	}
	pageInfo := timelineMedia.Get("page_info")
	items := timelineMedia.Get("edges").Array()
	if pageInfo.Get("has_next_page").Exists() {
		playlist.HasMore = true
		playlist.NextCursor = pageInfo.Get("end_cursor").String()
	}
	for _, item := range items {
		v := item.Get("node")
		textEdges := v.Get("edge_media_to_caption.edges").Array()
		post := &schema.Post{
			ID:         v.Get("id").Str,
			CreateTime: int(v.Get("taken_at_timestamp").Int()),
			Text:       textEdges[0].Get("node.text").String(),
		}
		medias, err := instagram.GetMediaListV2(v)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
		playlist.Posts = append(playlist.Posts, post)
	}
	if userNameFlag {
		if len(items) != 0 {
			playlist.User = &schema.User{
				Username: items[0].Get("node.owner.username").String(),
			}
		}
	} else {
		hashTag := result.Get("data.hashtag")
		playlist.User = &schema.User{
			Username: hashTag.Get("name").Str,
			Avatar:   hashTag.Get("profile_pic_url").Str,
		}
	}
	return playlist, nil
}
