package instagram_bulk_profile_scrapper

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/tidwall/gjson"
)

const (
	mediaAPI   = "https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/media_by_id"
	storiesAPI = "https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/ig_profile"
)

var (
	headers = map[string]string{
		"x-rapidapi-host": "instagram-bulk-profile-scrapper.p.rapidapi.com",
		"x-rapidapi-key":  "**************************************************",
	}
)

// https://rapidapi.com/thekirtan/api/instagram-bulk-profile-scrapper
// 快拍：
func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "stories" {
		if paths[1] == "highlights" {
			return getMediaByHighlightId(ctx, paths[2])
		}
		return getStoryByUserName(ctx, paths[1])
	}

	return getPostByShortCode(ctx, instagram.GetShortCodeByURL(req.URL))
}

func getStoryByUserName(ctx context.Context, userName string) (post *schema.Post, err error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(15 * time.Second).
		SetQueryParams(map[string]string{
			"ig":            userName,
			"response_type": "story",
		}).
		Get(storiesAPI)
	if err != nil {
		return nil, err
	}
	result := gjson.Parse(resp.String())
	if resp.IsError() || len(result.Array()) < 1 {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}

	stories := result.Array()[0].Get("story.data")
	if !stories.Exists() {
		return nil, extract.ErrNoStory
	}

	post = &schema.Post{
		Text: userName + "的快拍",
	}
	for _, story := range stories.Array() {
		medias, err := instagram.GetMediaList(story)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}
	return post, nil
}

func getPostByShortCode(ctx context.Context, shortCode string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(15 * time.Second).
		SetQueryParams(map[string]string{
			"shortcode":     shortCode,
			"response_type": "reel",
		}).
		Get(mediaAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	p := result.Array()[0].Get("items").Array()[0]
	commentCount := int(p.Get("comment_count").Int())
	diggCount := int(p.Get("like_count").Int())
	playCount := int(p.Get("play_count").Int())
	post := &schema.Post{
		Text: p.Get("caption.text").String(),
		Stats: &schema.Stats{
			CommentCount: &commentCount,
			DiggCount:    &diggCount,
			PlayCount:    &playCount,
		},
	}
	if p.Get("carousel_media").Array()[0].Get("image_versions2").Exists() {
		for _, story := range p.Get("carousel_media").Array() {
			medias, err := instagram.GetMediaList(story)
			if err != nil {
				return nil, err
			}
			post.Medias = append(post.Medias, medias...)
		}
	} else {
		medias, err := instagram.GetMediaList(p)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}

	return post, nil
}

// TODO 接口暂时维护
func getMediaByHighlightId(ctx context.Context, highlightID string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(15 * time.Second).
		SetQueryParams(map[string]string{
			"id":            "highlight:" + highlightID,
			"response_type": "highlight",
			"corsEnabled":   "false",
		}).
		Get(mediaAPI)
	if err != nil {
		return nil, err
	}
	result := gjson.Parse(resp.String())
	if resp.IsError() || len(result.Array()) == 0 {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}

	post := &schema.Post{
		Text: "快拍精选:",
	}
	for _, item := range result.Array()[0].Array() {
		medias, err := instagram.GetMediaList(item)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}
	return post, nil
}
