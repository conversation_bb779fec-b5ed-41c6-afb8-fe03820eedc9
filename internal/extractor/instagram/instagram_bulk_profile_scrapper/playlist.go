package instagram_bulk_profile_scrapper

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"resty.dev/v3"
)

const (
	userAPI = "https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/feeds"
	tagAPI  = "https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/media_by_tag"
)

func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	playlist = &schema.Playlist{}
	paths := urlx.ParsePath(req.URL)

	var resp *resty.Response
	if paths[0] == "explore" && paths[1] == "tags" {
		params := map[string]string{
			"tag":         paths[2],
			"feed_type":   "recent",
			"corsEnabled": "false",
		}
		if req.Cursor != "" {
			params["nextMaxId"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetQueryParams(params).
			Get(tagAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram_bulk_profile_scrapper.tag: %s", resp.String())
		}
	} else {
		userInfo, err := instagram.GetUserInfoByURL(ctx, req.URL, false)
		if err != nil {
			return nil, err
		}
		params := map[string]string{
			"ig":          userInfo.Username,
			"corsEnabled": "false",
		}
		if req.Cursor != "" {
			params["nextMaxId"] = req.Cursor
		}
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetQueryParams(params).
			Get(userAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] instagram_bulk_profile_scrapper.uid: %s", resp.String())
		}
	}
	result := gjson.Parse(resp.String())
	if len(result.Array()) == 0 {
		return nil, extract.ErrRetryable
	}
	if result.Get("error").Exists() {
		if result.Get("error").Str == "IgExactUserNotFoundError" {
			return nil, extract.ErrUserNotFound
		}
	}
	if result.Get("moreAvailable").Exists() || result.Get("more_available").Exists() {
		playlist.HasMore = true
		playlist.NextCursor = lo.CoalesceOrEmpty(result.Get("nextMaxId").String(), result.Get("next_max_id").String())
	}

	items := result.Get("items").Array()
	if len(items) == 0 {
		items = result.Get("data").Array()
	}

	for _, item := range items {
		createTime := int(item.Get("taken_at").Int())
		playCount := int(item.Get("play_count").Int())
		commentCount := int(item.Get("comment_count").Int())
		diggCount := int(item.Get("like_count").Int())
		post := &schema.Post{
			ID:         item.Get("pk").Str,
			CreateTime: createTime,
			Text:       item.Get("caption").Str,
			Stats: &schema.Stats{
				PlayCount:    &playCount,
				CommentCount: &commentCount,
				DiggCount:    &diggCount,
			},
		}
		medias, err := instagram.GetMediaList(item)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
		playlist.Posts = append(playlist.Posts, post)
	}

	if len(items) != 0 {
		playlist.User = &schema.User{
			Username: items[0].Get("user.username").Str,
			Avatar:   items[0].Get("user.profile_pic_url").Str,
		}
	}
	return playlist, nil
}
