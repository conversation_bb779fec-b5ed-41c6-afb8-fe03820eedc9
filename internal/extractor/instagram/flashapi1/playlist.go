package flashapi1

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/extractor/instagram/instagram_bulk_profile_scrapper"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/tidwall/gjson"
)

// 主页：https://rapidapi.com/for-sharm/api/flashapi1/playground/apiendpoint_ff099de6-81c6-4782-b57f-e8b7bbfe37a4
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	playlist = &schema.Playlist{}
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "explore" && paths[1] == "tags" {
		return instagram_bulk_profile_scrapper.ExtractPlaylist(ctx, req)
	}
	userInfo, err := instagram.GetUserInfoByURL(ctx, req.URL, true)
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"user": userInfo.Username,
	}
	if req.Cursor != "" {
		params["end_cursor"] = req.Cursor
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(20 * time.Second).
		SetQueryParams(params).
		Get(playlistAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] flashapi1: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	if !result.Get("user").Exists() {
		return nil, extract.ErrInvalidUserPageURL
	}
	items := result.Get("items").Array()
	if len(items) == 0 {
		return nil, extract.ErrRetryable
	}

	if result.Get("more_available").String() == "true" && result.Get("next_max_id").Exists() {
		playlist.HasMore = true
		playlist.NextCursor = result.Get("next_max_id").String()
	}

	for _, item := range items {
		post := &schema.Post{
			ID:         item.Get("id").String(),
			Text:       item.Get("caption.text").String(),
			CreateTime: int(item.Get("taken_at").Int()),
		}
		medias, err := instagram.GetMediaList(item)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
		playlist.Posts = append(playlist.Posts, post)
	}
	if len(items) != 0 {
		playlist.User = &schema.User{
			Username: items[0].Get("user.username").String(),
		}
	}

	return playlist, nil
}
