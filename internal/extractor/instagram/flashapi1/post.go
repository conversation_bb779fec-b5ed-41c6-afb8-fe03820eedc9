package flashapi1

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/instagram"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/tidwall/gjson"
)

const (
	postAPI       = "https://flashapi1.p.rapidapi.com/ig/post_info/"
	storiesAPI    = "https://flashapi1.p.rapidapi.com/ig/stories/"
	highlightsAPI = "https://flashapi1.p.rapidapi.com/ig/highlights/"
	playlistAPI   = "https://flashapi1.p.rapidapi.com/ig/posts_username/"
)

var headers = map[string]string{
	"x-rapidapi-host": "flashapi1.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

// 帖子： https://rapidapi.com/for-sharm/api/flashapi1/playground/apiendpoint_c48aefeb-6ed1-431f-b55c-61cd3a648b2e
// 快拍： https://rapidapi.com/for-sharm/api/flashapi1/playground/apiendpoint_328bc1ac-e331-4025-89de-9c94393c248f
// 快拍精选： https://rapidapi.com/for-sharm/api/flashapi1/playground/apiendpoint_0ead39f7-2a0b-4c94-8d3d-9522e1c4bcd0
func Extract(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	req.URL, err = instagram.GetLongURLWhenShort(ctx, req.URL)
	if err != nil {
		return nil, err
	}
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "stories" {
		if paths[1] == "highlights" {
			return getMediaByHighlightId(ctx, paths[2])
		}
		return getStoryByUserName(ctx, paths[1])
	}

	return getPostByShortCode(ctx, instagram.GetShortCodeByURL(req.URL))
}

func getStoryByUserName(ctx context.Context, username string) (post *schema.Post, err error) {
	userID, err := instagram.GetUserIDByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(20*time.Second).
		SetQueryParam("id_user", userID).
		Get(storiesAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	reelsMedias := result.Get("data.xdt_api__v1__feed__reels_media.reels_media").Array()
	stories := make([]gjson.Result, 0)
	for _, reels := range reelsMedias {
		stories = append(stories, reels.Get("item").Array()...)
	}
	if len(stories) == 0 {
		stories = append(stories, result.Get("reel.items").Array()...)
	}
	if len(stories) == 0 {
		return nil, extract.ErrNoStory
	}
	post = &schema.Post{
		Text: username + "的快拍",
	}
	for _, story := range stories {
		medias, err := instagram.GetMediaList(story)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}

	return post, nil
}

func getPostByShortCode(ctx context.Context, shortCode string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(20*time.Second).
		SetQueryParam("shortcode", shortCode).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	items := result.Get("data.xdt_api__v1__media__shortcode__web_info.items").Array()
	if len(items) == 0 {
		items = append(items, result.Get("items").Array()...)
	}
	if len(items) == 0 {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}

	text := items[0].Get("caption.text").String()
	commentCount := int(items[0].Get("comment_count").Int())
	diggCount := int(items[0].Get("like_count").Int())
	post := &schema.Post{
		Text: text,
		Stats: &schema.Stats{
			CommentCount: &commentCount,
			DiggCount:    &diggCount,
		},
	}
	for _, item := range items {
		medias, err := instagram.GetMediaList(item)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}
	return post, nil
}

func getMediaByHighlightId(ctx context.Context, highlightID string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetTimeout(20*time.Second).
		SetQueryParam("id_highlight", highlightID).
		Get(highlightsAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	edges := result.Get("data.xdt_api__v1__feed__reels_media__connection.items.edges").Array()
	items := make([]gjson.Result, 0)
	if len(edges) != 0 {
		for _, edge := range edges {
			items = append(items, edge.Get("node.items").Array()...)
		}
	}
	if len(items) == 0 {
		items = result.Get("reels").Get("highlight:" + highlightID).Get("items").Array()
	}
	if len(items) == 0 {
		return nil, fmt.Errorf("[post] flashapi1: %s", resp.String())
	}
	post := &schema.Post{
		Text: "快拍精选:" + highlightID,
	}
	for _, item := range items {
		medias, err := instagram.GetMediaList(item)
		if err != nil {
			return nil, err
		}
		post.Medias = append(post.Medias, medias...)
	}
	return post, nil
}
