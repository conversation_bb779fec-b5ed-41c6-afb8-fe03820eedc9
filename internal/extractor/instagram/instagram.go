package instagram

import (
	"context"
	"errors"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"garden/pkg/extract"
	"garden/pkg/rdb"
	"garden/pkg/useragent"
	"github.com/gongyinshi/shared/urlx"
	"github.com/redis/go-redis/v9"
	"golang.org/x/sync/singleflight"
	"log/slog"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"golang.org/x/exp/slices"
)

var (
	headers = map[string]string{
		"x-rapidapi-host": "flashapi1.p.rapidapi.com",
		"x-rapidapi-key":  "**************************************************",
	}
	instagramBulkProfileScrapperHeaders = map[string]string{
		"x-rapidapi-host": "instagram-bulk-profile-scrapper.p.rapidapi.com",
		"x-rapidapi-key":  "**************************************************",
	}
	MediaByIDAPI = "https://instagram-bulk-profile-scrapper.p.rapidapi.com/clients/api/ig/media_by_id"
	re           = regexp.MustCompile("/(p|tv|reel)/([A-Za-z0-9_-]+)")
	pathMap      = []string{"direct", "explore"}

	gsf singleflight.Group
)

// GetLongURLWhenShort 获取短链接对应的长链接
func GetLongURLWhenShort(ctx context.Context, shortURL string) (string, error) {
	for i := 0; i < 2; i++ {
		parsedURL, err := url.Parse(shortURL)
		if err != nil {
			return "", fmt.Errorf("failed to parse URL: %v", err)
		}

		path := parsedURL.Path
		if !strings.HasPrefix(path, "/s/") && !strings.HasPrefix(path, "/share/") {
			break
		}

		finalURL := redirect.ResolveFirstRedirectURL(ctx, shortURL, &redirect.Options{
			UserAgent: useragent.RandomMobile(),
		})
		if finalURL == shortURL {
			break
		}
		shortURL = finalURL
	}
	return shortURL, nil
}

// GetShortCodeByURL 从URL中提取Instagram的shortcode
func GetShortCodeByURL(url string) string {
	matches := re.FindStringSubmatch(url)
	if len(matches) < 3 {
		return ""
	}
	return matches[2]
}

// GetUserIDByUsername 根据用户名获取用户ID
func GetUserIDByUsername(ctx context.Context, username string) (string, error) {
	val, err, _ := gsf.Do(username, func() (interface{}, error) {
		userID, rerr := rdb.C.HGet(ctx, rdb.RedisInstagramUsernameMap.Key, username).Result()
		if rerr != nil && !errors.Is(rerr, redis.Nil) {
			slog.ErrorContext(ctx, "failed to get username", "username", username, "err", rerr)
		}
		if userID != "" {
			return userID, nil
		}

		// 尝试使用flashapi1
		resp, err := constant.RestyClient.R().SetContext(ctx).
			SetHeaders(headers).
			SetQueryParam("user", username).
			SetTimeout(15 * time.Second).
			Get("https://flashapi1.p.rapidapi.com/ig/web_profile_info/")
		if err != nil {
			return "", err
		}
		if resp.IsError() {
			return "", fmt.Errorf("instagram.GetUserIDByUsername err:%s", resp.String())
		}
		json := gjson.Parse(resp.String())
		if json.Get("status").String() != "ok" {
			return "", fmt.Errorf("instagram.GetUserIDByUsername err:%s", resp.String())
		}
		user := json.Get("data.user")
		if user.Get("is_private").Bool() {
			return "", extract.ErrNonPublicContent
		}
		userID = user.Get("id").String()
		if userID == "" {
			return "", extract.ErrUserNotFound
		}

		rdb.C.HSet(ctx, rdb.RedisInstagramUsernameMap.Key, username, userID)

		return userID, nil
	})
	if err != nil {
		return "", err
	}
	return val.(string), nil
}

func GetMediaList(story gjson.Result) ([]*schema.Media, error) {
	medias := make([]*schema.Media, 0)
	mediaType := story.Get("media_type").Int() // 1 图片  2 视频  8 图集
	if story.Get("video_versions").Exists() {
		mediaType = 2
	}
	if mediaType == 1 {
		imageURL := ExtractCover(story)
		media := schema.NewImageMedia(imageURL)
		medias = append(medias, media)
	} else if mediaType == 2 {
		imageURL := ExtractCover(story)
		videoVersions := story.Get("video_versions").Array()
		var resourceURL string
		var media *schema.Media
		for _, videoVersion := range videoVersions {
			if len(videoVersion.Get("candidates").Array()) != 0 {
				resourceURL = videoVersion.Get("candidates").Array()[0].Get("url").String()
			} else if len(videoVersions) != 0 {
				resourceURL = videoVersions[0].Get("url").String()
			} else {
				resourceURL = ""
			}
			if resourceURL != "" {
				media = schema.NewVideoMedia(resourceURL, imageURL)
			} else {
				media = schema.NewImageMedia(imageURL)
			}
		}
		medias = append(medias, media)
	} else if mediaType == 8 {
		for _, item := range story.Get("carousel_media").Array() {
			carouselMediams, err := GetMediaList(item)
			if err != nil {
				return nil, err
			}
			medias = append(medias, carouselMediams...)
		}
	} else if mediaType == 19 {
		return nil, fmt.Errorf("not support threads text_post type")
	} else {
		return nil, fmt.Errorf("ins出现未知media_type %d", mediaType)
	}

	return medias, nil
}

func GetMediaListV2(v gjson.Result) ([]*schema.Media, error) {
	medias := make([]*schema.Media, 0)
	mediaType := v.Get("__typename").String() // 1 图片  2 视频  8 图集
	if mediaType == "" {
		if v.Get("edge_sidecar_to_children").String() != "" {
			mediaType = "GraphSidecar"
		} else if v.Get("is_video").String() != "" {
			mediaType = "GraphVideo"
		} else {
			mediaType = "GraphImage"
		}
	}
	if slices.Contains([]string{"GraphImage", "XDTGraphImage"}, mediaType) {
		imageURL := ExtractCoverV2(v)
		medias = append(medias, schema.NewImageMedia(imageURL))
	} else if slices.Contains([]string{"GraphVideo", "XDTGraphVideo"}, mediaType) {
		imageURL := ExtractCoverV2(v)
		resourceURL := v.Get("video_url").String()
		if resourceURL != "" {
			medias = append(medias, schema.NewVideoMedia(resourceURL, imageURL))
		} else {
			medias = append(medias, schema.NewImageMedia(imageURL))
		}
	} else if mediaType == "GraphSidecar" {
		edges := v.Get("edge_sidecar_to_children.edges").Array()
		if len(edges) != 0 {
			for _, edge := range edges {
				m, err := GetMediaListV2(edge.Get("node"))
				if err != nil {
					return nil, err
				}
				medias = append(medias, m...)
			}

		} else {
			imageURL := ExtractCoverV2(v)
			media := schema.NewImageMedia(imageURL)
			medias = append(medias, media)
		}
	}

	return medias, nil
}

func ExtractCoverV2(v gjson.Result) string {
	resources := v.Get("display_resources").Array()
	if len(resources) == 0 {
		resources = v.Get("thumbnail_resources").Array()
	}
	if len(resources) != 0 {
		sort.Slice(resources, func(i, j int) bool {
			return resources[i].Get("config_width").Int() > resources[j].Get("config_width").Int()
		})
		return resources[0].Get("src").String()
	}
	return ""
}

func ExtractCover(result gjson.Result) string {
	candidates := result.Get("image_versions2").Get("candidates").Array()
	if len(candidates) != 0 {
		sort.Slice(candidates, func(i, j int) bool {
			return candidates[i].Get("width").Int() > candidates[j].Get("width").Int()
		})
		return candidates[0].Get("url").String()
	}
	return ""
}

func GetUserInfoByURL(ctx context.Context, rawURL string, noUID bool) (userInfo *UserInfo, err error) {
	userInfo = &UserInfo{}
	paths := urlx.ParsePath(rawURL)
	if len(paths) == 0 {
		return nil, extract.ErrInvalidUserPageURL
	}
	if GetShortCodeByURL(rawURL) != "" {
		userInfo, err = GetUserInfoByShortCode(ctx, GetShortCodeByURL(rawURL))
		if err != nil {
			return nil, err
		}
		return userInfo, nil
	} else if paths[0] == "stories" && paths[1] != "highlights" {
		userInfo.Username = paths[1]
	} else if !slices.Contains(pathMap, paths[0]) {
		userInfo.Username = paths[0]
	}
	if userInfo.Username == "" {
		return nil, extract.ErrInvalidUserPageURL
	}
	if noUID {
		return userInfo, nil
	}
	userInfo.UID, err = GetUserIDByUsername(ctx, userInfo.Username)
	if err != nil {
		return nil, err
	}

	return userInfo, nil
}

func GetUserInfoByShortCode(ctx context.Context, shortCode string) (userInfo *UserInfo, err error) {
	res, err, _ := gsf.Do(shortCode, func() (interface{}, error) {
		resp, err := constant.RestyClient.R().SetContext(ctx).
			SetHeaders(instagramBulkProfileScrapperHeaders).
			SetTimeout(15 * time.Second).
			SetQueryParams(map[string]string{
				"shortcode":     shortCode,
				"response_type": "feeds",
			}).
			Get(MediaByIDAPI)
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("instagram.GetUserInfoByShortCode err:%s", resp.String())
		}
		result := gjson.Parse(resp.String())
		if result.Exists() || result.Array()[0].Get("items").Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		user := result.Array()[0].Get("items").Array()[0].Get("user")
		if user.Exists() {
			return nil, extract.ErrInvalidUserPageURL
		}
		res := &UserInfo{
			Username: user.Get("username").String(),
			UID:      user.Get("pk").String(),
		}
		return res, nil
	})
	if err != nil {
		return nil, err
	}
	return res.(*UserInfo), nil
}
