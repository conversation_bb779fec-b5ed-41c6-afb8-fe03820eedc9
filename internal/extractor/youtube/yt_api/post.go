package yt_api

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/youtube"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"sort"
	"strconv"
	"strings"

	"github.com/funnyfactor/mimetype"
	"github.com/samber/lo"
)

const (
	postAPI = "https://yt-api.p.rapidapi.com/dl"
)

func randomHeaders() map[string]string {
	return map[string]string{
		"x-rapidapi-key": "**************************************************",
		//"x-rapidapi-key":  lo.<PERSON>ple([]string{"**************************************************", "690ae987f0mshf97f2dec4174023p1be898jsn89f4e912f1fd"}),
		"x-rapidapi-host": "yt-api.p.rapidapi.com",
	}
}

// https://rapidapi.com/ytjar/api/yt-api/playground/endpoint_facba415-c341-4af1-b542-6f17c9fc464a
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {

	vID, err := urlx.YoutubeVideoID(req.URL)
	if err != nil {
		return nil, err
	}
	var result YTResponse
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(randomHeaders()).
		SetQueryParams(map[string]string{
			"id":   vID,
			"cgeo": "US",
		}).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] yt_api: %s", resp.String())
	}

	// 文案：title  ||  description ||  channelTitle
	text := result.Title
	if result.Title == "" && result.ChannelTitle != "" {
		text = result.ChannelTitle
	}
	if result.ChannelTitle == "" && result.Description != "" {
		text = result.Description
	}

	post := &schema.Post{
		Text: text,
	}

	var thumbnail string // 封面
	if lo.LastOrEmpty(result.Thumbnail).URL != "" {
		thumbnail = lo.LastOrEmpty(result.Thumbnail).URL
	} else {
		thumbnail = urlx.YoutubeThumbnailURL(vID)
	}
	// adaptiveFormats、formats获取不同分辨率(质量)的视频地址
	// 放一起统一处理
	for _, format := range result.Formats {
		result.AdaptiveFormats = append(result.AdaptiveFormats, AdaptiveFormat{
			URL:           format.URL,
			MimeType:      format.MimeType,
			QualityLabel:  &format.QualityLabel,
			ContentLength: format.ContentLength,
			Height:        &format.Height,
			Width:         &format.Width,
		})
	}
	post.Medias = make([]*schema.Media, 0)

	// 筛选出符合要求的原始数据
	videoAdaptiveFormats := extractVideos(result.AdaptiveFormats)
	audioAdaptiveFormats := extractAudios(result.AdaptiveFormats)

	// 定义最后传回的
	videoFormats := make([]schema.Format, 0)
	audioFormats := make([]schema.Format, 0)

	var videoHasAudioFormat *AdaptiveFormat
	// 初步赋值
	for _, format := range videoAdaptiveFormats {
		quality := 0
		qualityNote := ""
		if format.QualityLabel != nil {
			splits := strings.Split(*format.QualityLabel, "p")
			qualityStr := ""
			if len(splits) > 0 {
				qualityStr = splits[0]
			}
			quality, _ = strconv.Atoi(qualityStr)
			if quality == 0 {
				continue
			}
			qualityNote = *format.QualityLabel
		}
		// 记录是否有音频
		hasAudio := hasAudioInVideo(format.MimeType)
		separate := 1
		if hasAudio {
			separate = 0
			videoHasAudioFormat = &format
		}
		size, _ := strconv.ParseInt(format.ContentLength, 10, 64)
		videoFormats = append(videoFormats, schema.Format{
			Quality:     quality,
			VideoURL:    format.URL,
			VideoExt:    mimetype.ExtensionByType(format.MimeType),
			VideoSize:   int(size),
			AudioURL:    "",
			AudioExt:    "",
			AudioSize:   0,
			Separate:    separate,
			QualityNote: qualityNote,
		})
	}

	// 音频format 存在与否
	isTrackExist := false
	languageAudioMap := make(map[string]struct{})
	// 音频赋值
	for _, format := range audioAdaptiveFormats {
		size, _ := strconv.Atoi(format.ContentLength)
		language := ""
		if format.AudioTrack != nil {
			language = format.AudioTrack.DisplayName
			isTrackExist = true
		}
		if _, ok := languageAudioMap[language]; !ok {
			audioFormats = append(audioFormats, schema.Format{
				AudioURL:  format.URL,
				AudioExt:  mimetype.ExtensionByType(format.MimeType),
				AudioSize: size,
				Separate:  1,
				Language:  language,
			})
			languageAudioMap[language] = struct{}{}
		}
	}
	if len(videoAdaptiveFormats) > 0 {
		var format *AdaptiveFormat
		// 优先使用有音频的视频
		if videoHasAudioFormat != nil {
			format = videoHasAudioFormat
		} else {
			format = &videoAdaptiveFormats[0]
		}
		videoMedia := schema.NewVideoMedia(format.URL, thumbnail)
		videoMedia.Formats = videoFormats
		post.Medias = append(post.Medias, videoMedia)
	}
	if len(audioAdaptiveFormats) > 0 {
		if !isTrackExist {
			audioFormats = make([]schema.Format, 0)
		}
		audioMedia := schema.NewAudioMedia(audioAdaptiveFormats[0].URL, thumbnail)
		audioMedia.Formats = audioFormats
		post.Medias = append(post.Medias, audioMedia)
	}
	return post, nil
}

func extractVideos(formats []AdaptiveFormat) []AdaptiveFormat {
	qualityMap := make(map[int][]AdaptiveFormat)
	for _, format := range formats {
		if format.Height == nil || !strings.HasPrefix(format.MimeType, "video") {
			continue
		}
		quality := "0p"
		if format.QualityLabel != nil {
			qualityLabel := *format.QualityLabel
			quality = strings.Split(qualityLabel, "p")[0]
		}
		qualityInt, err := strconv.Atoi(quality)
		if err != nil || qualityInt == 0 {
			continue
		}
		qualityMap[qualityInt] = append(qualityMap[qualityInt], format)
	}

	result := make([]AdaptiveFormat, 0)
	for _, quality := range youtube.QualityOrder {
		formats := qualityMap[quality]
		if len(formats) == 0 {
			continue
		}
		// 按优先级排序
		sort.SliceStable(formats, func(i, j int) bool {
			// 1. 包含音频优先
			hasAudioA := hasAudioInVideo(formats[i].MimeType)
			audioA := 0
			if hasAudioA {
				audioA = 1
			}
			hasAudioB := hasAudioInVideo(formats[j].MimeType)
			audioB := 0
			if hasAudioB {
				audioB = 1
			}
			if audioA != audioB {
				return audioA > audioB
			}

			// 2. 编码优先级
			codecA := youtube.GetBaseCodec(formats[i].MimeType)
			codecB := youtube.GetBaseCodec(formats[j].MimeType)
			if codecA != codecB {
				return youtube.VideoCodecPriority[codecA] > youtube.VideoCodecPriority[codecB]
			}

			// 3. 帧率
			defaultFps := 0
			if formats[i].Fps == nil {
				formats[i].Fps = &defaultFps
			}
			if formats[j].Fps == nil {
				formats[j].Fps = &defaultFps
			}
			if formats[i].Fps != formats[j].Fps {
				return *formats[i].Fps > *formats[j].Fps
			}

			// 4. 文件大小
			sizeA, err := strconv.ParseInt(formats[i].ContentLength, 10, 64)
			if err != nil {
				sizeA = 0
			}
			sizeB, err := strconv.ParseInt(formats[j].ContentLength, 10, 64)
			if err != nil {
				sizeB = 0
			}
			return sizeA > sizeB
		})
		if len(formats) > 0 {
			result = append(result, formats[0])
		}
	}
	return result
}

func extractAudios(formats []AdaptiveFormat) []AdaptiveFormat {
	audioFormats := make([]AdaptiveFormat, 0)
	for _, format := range formats {
		if !strings.HasPrefix(format.MimeType, "audio") {
			continue
		}
		audioFormats = append(audioFormats, format)
	}
	languageGroups := make(map[string][]AdaptiveFormat)
	for _, format := range audioFormats {
		lang := "default"
		if format.AudioTrack != nil {
			lang = format.AudioTrack.ID
		}
		languageGroups[lang] = append(languageGroups[lang], format)
	}

	result := make([]AdaptiveFormat, 0)
	for _, formats := range languageGroups {
		if len(formats) == 0 {
			continue
		}
		sort.Slice(formats, func(i, j int) bool {
			// 1. 编码优先级
			codecA, ok := youtube.AudioCodecPriority[youtube.GetBaseCodec(formats[i].MimeType)]
			if !ok {
				codecA = 1
			}
			codecB, ok := youtube.AudioCodecPriority[youtube.GetBaseCodec(formats[j].MimeType)]
			if !ok {
				codecB = 1
			}
			if codecA != codecB {
				return codecA > codecB
			}

			// 2. 比特率
			bitrateA := formats[i].AverageBitrate
			bitrateB := formats[j].AverageBitrate
			if bitrateA != bitrateB {
				return bitrateA > bitrateB
			}

			// 3. 采样率
			sampleRateA, _ := strconv.Atoi(*formats[i].AudioSampleRate)
			sampleRateB, _ := strconv.Atoi(*formats[j].AudioSampleRate)
			if sampleRateA != sampleRateB {
				return sampleRateA > sampleRateB
			}

			// 4. 文件大小
			sizeA, _ := strconv.Atoi(formats[i].ContentLength)
			sizeB, _ := strconv.Atoi(formats[j].ContentLength)
			return sizeA > sizeB
		})
		if len(formats) > 0 {
			result = append(result, formats...)
		}
	}
	return result
}

// 根据 MIME 类型检查视频是否包含音频。
func hasAudioInVideo(mimeType string) bool {
	codecs := strings.Split(mimeType, "codecs=")
	if len(codecs) > 1 {
		codec := codecs[1]
		codecs := strings.Split(codec, ",")
		return len(codecs) >= 2
	}
	return false
}
