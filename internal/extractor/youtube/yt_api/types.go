package yt_api

import "garden/internal/extractor/youtube"

type YTResponse struct {
	Status            string              `json:"status"`
	ID                string              `json:"id"`
	Title             string              `json:"title"`
	LengthSeconds     string              `json:"lengthSeconds"`
	Keywords          []string            `json:"keywords"`
	ChannelTitle      string              `json:"channelTitle"`
	ChannelID         string              `json:"channelId"`
	Description       string              `json:"description"`
	Thumbnail         []youtube.Thumbnail `json:"thumbnail"`
	AllowRatings      bool                `json:"allowRatings"`
	ViewCount         string              `json:"viewCount"`
	IsPrivate         bool                `json:"isPrivate"`
	IsUnpluggedCorpus bool                `json:"isUnpluggedCorpus"`
	IsLiveContent     bool                `json:"isLiveContent"`
	Storyboards       []Storyboard        `json:"storyboards"`
	Captions          Captions            `json:"captions"`
	ExpiresInSeconds  string              `json:"expiresInSeconds"`
	Formats           []Format            `json:"formats"`
	AdaptiveFormats   []AdaptiveFormat    `json:"adaptiveFormats"`
}

type AdaptiveFormat struct {
	Itag             int     `json:"itag"`
	URL              string  `json:"url"`
	MimeType         string  `json:"mimeType"`
	Bitrate          int     `json:"bitrate"`
	Width            *int    `json:"width,omitempty"`
	Height           *int    `json:"height,omitempty"`
	ContentLength    string  `json:"contentLength"`
	Quality          string  `json:"quality"`
	Fps              *int    `json:"fps,omitempty"`
	QualityLabel     *string `json:"qualityLabel,omitempty"`
	ProjectionType   string  `json:"projectionType"`
	AverageBitrate   int     `json:"averageBitrate"`
	ApproxDurationMs string  `json:"approxDurationMs"`
	HighReplication  *bool   `json:"highReplication,omitempty"`
	AudioQuality     *string `json:"audioQuality,omitempty"`
	AudioSampleRate  *string `json:"audioSampleRate,omitempty"`
	AudioChannels    *int    `json:"audioChannels,omitempty"`
	AudioTrack       *struct {
		DisplayName    string `json:"displayName"`
		ID             string `json:"id"`
		AudioIsDefault bool   `json:"audioIsDefault"`
	} `json:"audioTrack,omitempty"`
	LoudnessDb *float64 `json:"loudnessDb,omitempty"`
}

type Storyboard struct {
	Width           string   `json:"width"`
	Height          string   `json:"height"`
	ThumbsCount     string   `json:"thumbsCount"`
	Columns         string   `json:"columns"`
	Rows            string   `json:"rows"`
	Interval        string   `json:"interval"`
	StoryboardCount int      `json:"storyboardCount"`
	URL             []string `json:"url"`
}

type Captions struct {
	CaptionTracks        []CaptionTrack        `json:"captionTracks"`
	TranslationLanguages []TranslationLanguage `json:"translationLanguages"`
}

type CaptionTrack struct {
	BaseURL        string `json:"baseUrl"`
	Name           string `json:"name"`
	VssID          string `json:"vssId"`
	LanguageCode   string `json:"languageCode"`
	IsTranslatable bool   `json:"isTranslatable"`
}

type TranslationLanguage struct {
	LanguageCode string `json:"languageCode"`
	LanguageName string `json:"languageName"`
}

type Format struct {
	Itag             int    `json:"itag"`
	URL              string `json:"url"`
	MimeType         string `json:"mimeType"`
	Bitrate          int    `json:"bitrate"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	ContentLength    string `json:"contentLength,omitempty"`
	Quality          string `json:"quality"`
	Fps              int    `json:"fps"`
	QualityLabel     string `json:"qualityLabel"`
	ProjectionType   string `json:"projectionType"`
	AverageBitrate   *int   `json:"averageBitrate,omitempty"`
	ApproxDurationMs string `json:"approxDurationMs"`
	AudioSampleRate  string `json:"audioSampleRate"`
	AudioChannels    int    `json:"audioChannels"`
}

type VideoInfo struct {
	URL               string  `json:"url"`
	Extension         string  `json:"extension"`
	AlternateURL      *string `json:"alternateUrl,omitempty"`
	Quality           string  `json:"quality"`
	ContentLength     int     `json:"contentLength"`
	HasAudio          bool    `json:"hasAudio"`
	IsHorizontalVideo bool    `json:"isHorizontalVideo"`
}

type AudioInfo struct {
	URL          string  `json:"url"`
	Extension    string  `json:"extension"`
	AlternateURL *string `json:"alternateUrl,omitempty"`
	DisplayName  string  `json:"displayName"`
}

type SubtitlesResult struct {
	ID                 string              `json:"id"`
	Title              string              `json:"title"`
	LengthSeconds      string              `json:"lengthSeconds"`
	Keywords           []string            `json:"keywords"`
	ChannelTitle       string              `json:"channelTitle"`
	ChannelID          string              `json:"channelId"`
	Description        string              `json:"description"`
	Thumbnail          []youtube.Thumbnail `json:"thumbnail"`
	AllowRatings       bool                `json:"allowRatings"`
	ViewCount          string              `json:"viewCount"`
	IsPrivate          bool                `json:"isPrivate"`
	IsUnpluggedCorpus  bool                `json:"isUnpluggedCorpus"`
	IsLiveContent      bool                `json:"isLiveContent"`
	IsCrawlable        bool                `json:"isCrawlable"`
	IsFamilySafe       bool                `json:"isFamilySafe"`
	AvailableCountries []string            `json:"availableCountries"`
	IsUnlisted         bool                `json:"isUnlisted"`
	Category           string              `json:"category"`
	PublishDate        string              `json:"publishDate"`
	PublishedAt        string              `json:"publishedAt"`
	UploadDate         string              `json:"uploadDate"`
	IsShortsEligible   bool                `json:"isShortsEligible"`
	Subtitles          struct {
		Subtitles []struct {
			LanguageName   string `json:"languageName"`
			LanguageCode   string `json:"languageCode"`
			IsTranslatable bool   `json:"isTranslatable"`
			URL            string `json:"url"`
		} `json:"subtitles"`
		Format               string `json:"format"`
		TranslationLanguages []struct {
			LanguageCode string `json:"languageCode"`
			LanguageName string `json:"languageName"`
		} `json:"translationLanguages"`
	} `json:"subtitles"`
	Storyboards []struct {
		Width           string   `json:"width"`
		Height          string   `json:"height"`
		ThumbsCount     string   `json:"thumbsCount"`
		Columns         string   `json:"columns"`
		Rows            string   `json:"rows"`
		Interval        string   `json:"interval"`
		StoryboardCount int      `json:"storyboardCount"`
		URL             []string `json:"url"`
	} `json:"storyboards"`
	PlayableInEmbed bool `json:"playableInEmbed"`
}
