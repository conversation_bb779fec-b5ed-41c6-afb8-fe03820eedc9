package yt_api

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/youtube"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"strconv"
	"time"

	"github.com/samber/lo"
)

const API = "https://yt-api.p.rapidapi.com/video/info"

// https://rapidapi.com/ytjar/api/yt-api/playground/endpoint_7a15a536-16f8-4d81-9509-1b9e042d1592
func ExtractSubtitles(ctx context.Context, req *schema.ExtractReq) (subtitlesResp *schema.SubtitlesResp, err error) {
	ID, err := urlx.YoutubeVideoID(req.URL)
	if err != nil {
		return nil, err
	}
	var result SubtitlesResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(randomHeaders()).
		SetQueryParam("id", ID).
		SetResult(&result).
		Get(API)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[subtitles] yt_api: %s", resp.String())
	}
	text := lo.CoalesceOrEmpty(result.Title, result.Description)
	duration, err := strconv.Atoi(result.LengthSeconds)
	if err != nil {
		return nil, err
	}
	publishedAt, err := time.Parse(time.RFC3339, result.PublishedAt)
	if err != nil {
		return nil, err
	}
	thumbnailURL := youtube.GetLargeThumbnailURL(result.Thumbnail)
	subtitles := make([]*schema.Subtitle, 0)
	for _, subtitle := range result.Subtitles.Subtitles {
		subtitleURLs := youtube.MakeSubtitleURLs(subtitle.URL)
		s := schema.Subtitle{
			LanguageName: subtitle.LanguageName,
			LanguageCode: subtitle.LanguageCode,
			URLs:         subtitleURLs,
		}
		subtitles = append(subtitles, &s)
	}
	subtitlesResp = &schema.SubtitlesResp{
		ID:           result.ID,
		Text:         text,
		Description:  result.Description,
		Duration:     &duration,
		PublishedAt:  &publishedAt,
		ThumbnailURL: thumbnailURL,
		Subtitles:    subtitles,
	}
	return subtitlesResp, nil
}
