package yt_api

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://www.youtube.com/watch?v=h9GZBw4-Ltk
// https://www.youtube.com/watch?v=snUsd9FevJE
// 短链接：https://youtu.be/RlNhD0oS5pk
// https://youtu.be/g4U4BQW9OEk
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://youtu.be/g4U4BQW9OEk",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}

// https://youtu.be/H14bBuluwB8
// https://www.youtube.com/watch?v=IVCs2mBoKUw
// https://www.youtube.com/watch?v=EXxMr7g6eMI
// https://www.youtube.com/watch?v=1SzB_bh0XaM&t=1s
func TestExtractSubtitles(t *testing.T) {
	subtitlesResp, err := ExtractSubtitles(context.Background(), &schema.ExtractReq{
		URL: "https://www.youtube.com/watch?v=1SzB_bh0XaM&t=1s",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(subtitlesResp.Subtitles[0].URLs[5])
}
