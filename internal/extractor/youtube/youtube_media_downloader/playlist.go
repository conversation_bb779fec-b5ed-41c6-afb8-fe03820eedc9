package youtube_media_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/youtube"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

// 定义类型映射
var (
	typeMap = map[string]string{
		"streams": "live",
		"shorts":  "shorts",
		"videos":  "videos",
	}
	re = regexp.MustCompile(`\D+`)
)

// Channel: https://rapidapi.com/DataFanatic/api/youtube-media-downloader/playground/apiendpoint_d3cf067c-c89c-428b-ad40-7c59569e00fe
// Playlist: https://rapidapi.com/DataFanatic/api/youtube-media-downloader/playground/apiendpoint_eb97e642-18d2-403a-98e0-10f5b3115f13
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	var channelID, postType, playlistID string
	// 请求参数赋值
	paths := urlx.ParsePath(req.URL)
	if paths[0] == "channel" {
		channelID = paths[1]
		postType = lo.NthOrEmpty(paths, 2)
	} else if paths[0] == "c" || paths[0] == "user" {
		channelID, err = unquotePlus(paths[1])
		if err != nil {
			return nil, err
		}
		postType = lo.NthOrEmpty(paths, 2)
	} else if paths[0] == "playlist" || paths[0] == "watch" {
		playlistID, err = getPlaylistID(req.URL)
	} else if strings.HasPrefix(paths[0], "@") {
		channelID, err = unquotePlus(paths[0])
		if err != nil {
			return nil, err
		}
		postType = lo.NthOrEmpty(paths, 1)
	}

	if channelID == "" && playlistID == "" {
		return nil, extract.ErrInvalidPlaylistURL
	}

	if req.Cursor != "" && req.Cursor != "no_more" {
		result := &ChannelResult{}
		resp, err := constant.RestyClient.R().SetContext(ctx).
			SetHeaders(randomHeaders()).
			SetBody(map[string]string{
				"nextToken": req.Cursor,
			}).
			SetResult(&result).
			Post("https://youtube-media-downloader.p.rapidapi.com/v2/misc/list-items")
		if err != nil {
			return nil, err
		}
		if resp.IsError() {
			return nil, fmt.Errorf("[playlist] youtube_media_downloader: %s", resp.String())
		}
		playlist, err = getPlaylistFromChannelResult(result, channelID)
		if err != nil {
			return nil, err
		}
	} else {
		params := map[string]string{
			"channelId": channelID,
			"type":      lo.ValueOr(typeMap, postType, "videos"),
		}

		if channelID != "" {
			result := &ChannelResult{}
			resp, err := constant.RestyClient.R().SetContext(ctx).
				SetHeaders(randomHeaders()).
				SetQueryParams(params).
				SetResult(&result).
				Get(channelAPI)
			if err != nil {
				return nil, err
			}
			if resp.IsError() {
				return nil, fmt.Errorf("[playlist] youtube_media_downloader: %s", resp.String())
			}
			playlist, err = getPlaylistFromChannelResult(result, channelID)
			if err != nil {
				return nil, err
			}
		} else {
			params["playlistId"] = playlistID
			result := &PlaylistResult{}
			resp, err := constant.RestyClient.R().SetContext(ctx).
				SetHeaders(randomHeaders()).
				SetQueryParams(params).
				SetResult(&result).
				Get(playlistAPI)
			if err != nil {
				return nil, err
			}
			if resp.IsError() {
				return nil, fmt.Errorf("[playlist] youtube_media_downloader: %s", resp.String())
			}
			playlist, err = getPlaylistFromPlaylistResult(result, playlistID)
			if err != nil {
				return nil, err
			}
		}
	}

	return playlist, nil
}

func getPlaylistFromPlaylistResult(result *PlaylistResult, playlistID string) (playlist *schema.Playlist, err error) {
	playlist = &schema.Playlist{}
	if result.NextToken != "" {
		playlist.HasMore = true
		playlist.NextCursor = result.NextToken
	}
	for _, item := range result.Items {
		videoID := item.ID
		videoURL, err := youtube.GotoService.GenerateGotoURL(schema.Payload{
			"url": fmt.Sprintf("https://www.youtube.com/watch?v=%v", videoID),
		}, false, map[string]string{
			"youtubeVideoId": videoID,
		})
		if err != nil {
			return nil, err
		}
		coverURL := urlx.YoutubeThumbnailURL(videoID)
		post := &schema.Post{
			ID:   videoID,
			Text: item.Title,
		}
		media := schema.NewVideoMedia(videoURL, coverURL)
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}
	playlist.User = &schema.User{
		Username: playlistID,
	}
	return playlist, nil
}

func getPlaylistFromChannelResult(result *ChannelResult, channelID string) (playlist *schema.Playlist, err error) {
	playlist = &schema.Playlist{}
	if result.NextToken != "" {
		playlist.HasMore = true
		playlist.NextCursor = result.NextToken
	}
	for _, item := range result.Items {
		videoID := item.ID
		videoURL, err := youtube.GotoService.GenerateGotoURL(schema.Payload{
			"url": fmt.Sprintf("https://www.youtube.com/watch?v=%v", videoID),
		}, false, map[string]string{
			"youtubeVideoId": videoID,
		})
		coverURL := urlx.YoutubeThumbnailURL(videoID)
		playCount, err := strconv.Atoi(re.ReplaceAllString(item.ViewCountText, ""))
		if err != nil {
			playCount = 0
		}
		post := &schema.Post{
			ID:   videoID,
			Text: item.Title,
			Stats: &schema.Stats{
				PlayCount: &playCount,
			},
		}
		media := schema.NewVideoMedia(videoURL, coverURL)
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}
	playlist.User = &schema.User{
		Username: channelID,
	}
	return playlist, nil
}

func unquotePlus(s string) (string, error) {
	s = strings.ReplaceAll(s, "+", " ")
	return url.QueryUnescape(s)
}

func getPlaylistID(postURL string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(postURL)
	if err != nil {
		return "", err
	}
	// 获取查询参数
	queryParams := parsedURL.Query()

	// 获取list参数的值
	listValue := queryParams.Get("list")

	return listValue, nil
}
