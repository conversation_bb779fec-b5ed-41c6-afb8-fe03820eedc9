package youtube_media_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/youtube"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"time"

	"github.com/samber/lo"
)

// https://rapidapi.com/DataFanatic/api/youtube-media-downloader/playground/apiendpoint_4fa33495-67eb-4f49-98d4-5ac71a2be5b0
// 和 单个帖子 是同一个接口，但是参数不一样，参数 urlAccess=blocked、subtitles=true、related=false
func ExtractSubtitles(ctx context.Context, req *schema.ExtractReq) (subtitlesResp *schema.SubtitlesResp, err error) {
	videoId, err := urlx.YoutubeVideoID(req.URL)
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"videoId":   videoId,
		"urlAccess": "blocked",
		"subtitles": "true",
		"related":   "false",
	}
	var result *SubtitlesResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(randomHeaders()).
		SetQueryParams(params).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[subtitles] youtube_media_downloader: %s", resp.String())
	}

	text := lo.CoalesceOrEmpty(result.Title, result.Description)
	publishedAt, err := time.Parse(time.RFC3339, result.PublishedTime)
	if err != nil {
		return nil, err
	}
	thumbnailURL := youtube.GetLargeThumbnailURL(result.Thumbnails)
	subtitles := make([]*schema.Subtitle, 0)
	for _, subtitle := range result.Subtitles.Items {
		subtitleURLs := youtube.MakeSubtitleURLs(subtitle.URL)
		s := schema.Subtitle{
			LanguageName: subtitle.Text,
			LanguageCode: subtitle.Code,
			URLs:         subtitleURLs,
		}
		subtitles = append(subtitles, &s)
	}
	subtitlesResp = &schema.SubtitlesResp{
		ID:           result.ID,
		Text:         text,
		Description:  result.Description,
		Duration:     &result.LengthSeconds,
		PublishedAt:  &publishedAt,
		ThumbnailURL: thumbnailURL,
		Subtitles:    subtitles,
	}
	return subtitlesResp, nil
}
