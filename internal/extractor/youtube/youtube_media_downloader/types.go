package youtube_media_downloader

import "garden/internal/extractor/youtube"

type postResult struct {
	Status      bool   `json:"status"`
	ErrorID     string `json:"errorId"`
	Type        string `json:"type"`
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Channel     struct {
		Type                string `json:"type"`
		ID                  string `json:"id"`
		Name                string `json:"name"`
		Handle              string `json:"handle"`
		IsVerified          bool   `json:"isVerified"`
		IsVerifiedArtist    bool   `json:"isVerifiedArtist"`
		SubscriberCountText string `json:"subscriberCountText"`
		Avatar              []struct {
			URL    string `json:"url"`
			Width  int    `json:"width"`
			Height int    `json:"height"`
		} `json:"avatar"`
	} `json:"channel"`
	LengthSeconds      int    `json:"lengthSeconds"`
	ViewCount          int    `json:"viewCount"`
	LikeCount          int    `json:"likeCount"`
	PublishedTime      string `json:"publishedTime"`
	PublishedTimeText  string `json:"publishedTimeText"`
	IsLiveStream       bool   `json:"isLiveStream"`
	IsLiveNow          bool   `json:"isLiveNow"`
	IsRegionRestricted bool   `json:"isRegionRestricted"`
	IsUnlisted         bool   `json:"isUnlisted"`
	IsCommentDisabled  bool   `json:"isCommentDisabled"`
	CommentCountText   string `json:"commentCountText"`
	Thumbnails         []struct {
		URL    string `json:"url"`
		Width  int    `json:"width"`
		Height int    `json:"height"`
	} `json:"thumbnails"`
	MusicCredits []struct {
		VideoID     string `json:"videoId"`
		Title       string `json:"title"`
		Artist      string `json:"artist"`
		Album       string `json:"album"`
		Description string `json:"description"`
		Thumbnails  []struct {
			URL string `json:"url"`
		} `json:"thumbnails"`
	} `json:"musicCredits"`
	Videos struct {
		Status     bool        `json:"status"`
		ErrorID    string      `json:"errorId"`
		Expiration int         `json:"expiration"`
		Items      []VideoItem `json:"items"`
	} `json:"videos"`
	Audios struct {
		Status     bool        `json:"status"`
		ErrorID    string      `json:"errorId"`
		Expiration int         `json:"expiration"`
		Items      []AudioItem `json:"items"`
	} `json:"audios"`
	Subtitles struct {
		Status     bool   `json:"status"`
		ErrorID    string `json:"errorId"`
		Expiration int    `json:"expiration"`
		Items      []struct {
			URL  string `json:"url"`
			Code string `json:"code"`
			Text string `json:"text"`
		} `json:"items"`
	} `json:"subtitles"`
	Related struct {
		NextToken string `json:"nextToken"`
		Items     []struct {
			Type    string `json:"type"`
			ID      string `json:"id"`
			Title   string `json:"title"`
			Channel struct {
				Type             string `json:"type"`
				ID               string `json:"id"`
				Name             string `json:"name"`
				IsVerified       bool   `json:"isVerified"`
				IsVerifiedArtist bool   `json:"isVerifiedArtist"`
				Avatar           []struct {
					URL    string `json:"url"`
					Width  int    `json:"width"`
					Height int    `json:"height"`
				} `json:"avatar"`
			} `json:"channel"`
			IsLiveNow         bool   `json:"isLiveNow,omitempty"`
			LengthText        string `json:"lengthText,omitempty"`
			ViewCountText     string `json:"viewCountText,omitempty"`
			PublishedTimeText string `json:"publishedTimeText,omitempty"`
			Thumbnails        []struct {
				URL    string `json:"url"`
				Width  int    `json:"width"`
				Height int    `json:"height"`
				Moving bool   `json:"moving"`
			} `json:"thumbnails"`
			VideoCountText string `json:"videoCountText,omitempty"`
		} `json:"items"`
	} `json:"related"`
}

type VideoItem struct {
	URL          string `json:"url"`
	LengthMs     int    `json:"lengthMs"`
	MimeType     string `json:"mimeType"`
	Extension    string `json:"extension"`
	LastModified int64  `json:"lastModified"`
	Size         int    `json:"size"`
	SizeText     string `json:"sizeText"`
	HasAudio     bool   `json:"hasAudio"`
	Quality      string `json:"quality"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
}

type AudioItem struct {
	URL          string `json:"url"`
	LengthMs     int    `json:"lengthMs"`
	MimeType     string `json:"mimeType"`
	Extension    string `json:"extension"`
	LastModified int64  `json:"lastModified"`
	Size         int    `json:"size"`
	SizeText     string `json:"sizeText"`
	IsDrc        bool   `json:"isDrc"`
}

type ChannelResult struct {
	Status        bool   `json:"status"`
	StatusDevNote string `json:"status_devNote"`
	ErrorID       string `json:"errorId"`
	NextToken     string `json:"nextToken"`
	Items         []struct {
		Type              string `json:"type"`
		ID                string `json:"id"`
		Title             string `json:"title"`
		IsLiveNow         bool   `json:"isLiveNow"`
		LengthText        string `json:"lengthText"`
		ViewCountText     string `json:"viewCountText"`
		PublishedTimeText string `json:"publishedTimeText"`
		Thumbnails        []struct {
			URL    string `json:"url"`
			Width  int    `json:"width"`
			Height int    `json:"height"`
			Moving bool   `json:"moving"`
		} `json:"thumbnails"`
	} `json:"items"`
}

type PlaylistResult struct {
	Status    bool   `json:"status"`
	ErrorID   string `json:"errorId"`
	NextToken string `json:"nextToken"`
	Items     []struct {
		Type    string `json:"type"`
		Index   int    `json:"index"`
		ID      string `json:"id"`
		Title   string `json:"title"`
		Channel struct {
			Type string `json:"type"`
			ID   string `json:"id"`
			Name string `json:"name"`
		} `json:"channel"`
		LengthText string `json:"lengthText"`
		Thumbnails []struct {
			URL    string `json:"url"`
			Width  int    `json:"width"`
			Height int    `json:"height"`
		} `json:"thumbnails"`
	} `json:"items"`
}

type SubtitlesResult struct {
	Status        bool   `json:"status"`
	StatusDevNote string `json:"status_devNote"`
	ErrorID       string `json:"errorId"`
	Type          string `json:"type"`
	ID            string `json:"id"`
	Title         string `json:"title"`
	Description   string `json:"description"`
	Channel       struct {
		Type                string `json:"type"`
		ID                  string `json:"id"`
		Name                string `json:"name"`
		Handle              string `json:"handle"`
		IsVerified          bool   `json:"isVerified"`
		IsVerifiedArtist    bool   `json:"isVerifiedArtist"`
		SubscriberCountText string `json:"subscriberCountText"`
		Avatar              []struct {
			URL    string `json:"url"`
			Width  int    `json:"width"`
			Height int    `json:"height"`
		} `json:"avatar"`
	} `json:"channel"`
	LengthSeconds      int                 `json:"lengthSeconds"`
	ViewCount          int                 `json:"viewCount"`
	LikeCount          int                 `json:"likeCount"`
	PublishedTime      string              `json:"publishedTime"`
	PublishedTimeText  string              `json:"publishedTimeText"`
	IsLiveStream       bool                `json:"isLiveStream"`
	IsLiveNow          bool                `json:"isLiveNow"`
	IsRegionRestricted bool                `json:"isRegionRestricted"`
	IsUnlisted         bool                `json:"isUnlisted"`
	IsCommentDisabled  bool                `json:"isCommentDisabled"`
	CommentCountText   string              `json:"commentCountText"`
	Thumbnails         []youtube.Thumbnail `json:"thumbnails"`
	MusicCredits       []struct {
		VideoID     string `json:"videoId"`
		Title       string `json:"title"`
		Artist      string `json:"artist"`
		Album       string `json:"album"`
		Description string `json:"description"`
		Thumbnails  []struct {
			URL string `json:"url"`
		} `json:"thumbnails"`
	} `json:"musicCredits"`
	Subtitles struct {
		Status        bool   `json:"status"`
		StatusDevNote string `json:"status_devNote"`
		ErrorID       string `json:"errorId"`
		Expiration    int    `json:"expiration"`
		Items         []struct {
			URL  string `json:"url"`
			Code string `json:"code"`
			Text string `json:"text"`
		} `json:"items"`
	} `json:"subtitles"`
}
