package youtube_media_downloader

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/youtube"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
	"sort"
	"strconv"
	"strings"

	"github.com/funnyfactor/mimetype"
	"github.com/samber/lo"
)

const (
	postAPI     = "https://youtube-media-downloader.p.rapidapi.com/v2/video/details"
	channelAPI  = "https://youtube-media-downloader.p.rapidapi.com/v2/channel/videos"
	playlistAPI = "https://youtube-media-downloader.p.rapidapi.com/v2/playlist/videos"
)

func randomHeaders() map[string]string {
	return map[string]string{
		"x-rapidapi-host": "youtube-media-downloader.p.rapidapi.com",
		// Personal xWorld xSpace xHello
		"x-rapidapi-key": lo.Sample([]string{"**************************************************", "690ae987f0mshf97f2dec4174023p1be898jsn89f4e912f1fd"}),

		// "93cbf5318cmsh1468b0cdd771783p1d0d91jsn25b20b4575a9",
		// "e22cd600bcmsh7ba817a344753d0p13c67cjsn43bd2aa0a15e",
	}
}

// https://rapidapi.com/DataFanatic/api/youtube-media-downloader/playground/apiendpoint_4fa33495-67eb-4f49-98d4-5ac71a2be5b0
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	vID, err := urlx.YoutubeVideoID(req.URL)
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"videoId":   vID,
		"subtitles": "false",
		"related":   "false",
	}
	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(randomHeaders()).
		SetQueryParams(params).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() || result.ErrorID != "Success" || len(result.Videos.Items) == 0 {
		return nil, fmt.Errorf("[post] youtube_media_downloader_test: %s", resp.String())
	}
	text := lo.CoalesceOrEmpty(result.Title, result.Description)
	post := &schema.Post{
		Text: text,
	}
	thumbnail := lo.LastOrEmpty(result.Thumbnails).URL
	if thumbnail == "" {
		thumbnail = urlx.YoutubeThumbnailURL(vID)
	}

	// 筛选出符合要求的原始数据
	videoAdaptiveFormats := downloaderExtractVideos(result.Videos.Items)
	audioAdaptiveFormats := downloaderExtractAudios(result.Audios.Items)

	// 定义最后传回的
	videoFormats := make([]schema.Format, 0)
	audioFormats := make([]schema.Format, 0)

	var videoHasAudioFormat *VideoItem

	for _, format := range videoAdaptiveFormats {
		quality := 0
		qualityNote := ""
		if format.Quality != "" {
			splits := strings.Split(format.Quality, "p")
			qualityStr := ""
			if len(splits) > 0 {
				qualityStr = splits[0]
			}
			quality, _ = strconv.Atoi(qualityStr)
			if quality == 0 {
				continue
			}
			qualityNote = format.Quality
		}
		hasAudio := format.HasAudio
		separate := 1
		if hasAudio {
			separate = 0
			videoHasAudioFormat = &format
		}

		videoFormats = append(videoFormats, schema.Format{
			Quality:     quality,
			VideoURL:    format.URL,
			VideoExt:    mimetype.ExtensionByType(format.MimeType),
			VideoSize:   format.Size,
			AudioURL:    "",
			AudioExt:    "",
			AudioSize:   0,
			Separate:    separate,
			QualityNote: qualityNote,
		})
	}
	// 音频赋值
	for _, format := range audioAdaptiveFormats {
		audioFormats = append(audioFormats, schema.Format{
			AudioURL:  format.URL,
			AudioExt:  mimetype.ExtensionByType(format.MimeType),
			AudioSize: format.Size,
			Separate:  1,
		})
	}
	if len(videoAdaptiveFormats) > 0 {
		var format *VideoItem
		// 优先使用有音频的视频
		if videoHasAudioFormat != nil {
			format = videoHasAudioFormat
		} else {
			format = &videoAdaptiveFormats[0]
		}
		videoMedia := schema.NewVideoMedia(format.URL, thumbnail)
		videoMedia.Formats = videoFormats
		post.Medias = append(post.Medias, videoMedia)
	}
	if len(audioAdaptiveFormats) > 0 {
		audioMedia := schema.NewAudioMedia(audioAdaptiveFormats[0].URL, thumbnail)
		audioMedia.Formats = audioFormats
		post.Medias = append(post.Medias, audioMedia)
	}

	return post, nil
}

func downloaderExtractAudios(items []AudioItem) []AudioItem {
	audioFormats := make([]AudioItem, 0)
	for _, format := range items {
		if !strings.HasPrefix(format.MimeType, "audio") {
			continue
		}
		audioFormats = append(audioFormats, format)
	}

	sort.Slice(audioFormats, func(i, j int) bool {
		// 1. 编码优先级
		codecA, ok := youtube.AudioCodecPriority[youtube.GetBaseCodec(audioFormats[i].MimeType)]
		if !ok {
			codecA = 1
		}
		codecB, ok := youtube.AudioCodecPriority[youtube.GetBaseCodec(audioFormats[j].MimeType)]
		if !ok {
			codecB = 1
		}
		if codecA != codecB {
			return codecA > codecB
		}

		return audioFormats[i].Size > audioFormats[j].Size
	})

	return audioFormats
}

func downloaderExtractVideos(items []VideoItem) []VideoItem {
	qualityMap := make(map[int][]VideoItem)
	for _, format := range items {
		if format.Height == 0 || !strings.HasPrefix(format.MimeType, "video") {
			continue
		}
		quality := "0p"
		if format.Quality != "" {
			qualityLabel := format.Quality
			quality = strings.Split(qualityLabel, "p")[0]
		}
		qualityInt, err := strconv.Atoi(quality)
		if err != nil || qualityInt == 0 {
			continue
		}
		qualityMap[qualityInt] = append(qualityMap[qualityInt], format)
	}

	result := make([]VideoItem, 0)
	for _, quality := range youtube.QualityOrder {
		formats := qualityMap[quality]
		if len(formats) == 0 {
			continue
		}
		// 按优先级排序
		sort.SliceStable(formats, func(i, j int) bool {
			// 1. 包含音频优先
			hasAudioA := formats[i].HasAudio
			audioA := 0
			if hasAudioA {
				audioA = 1
			}
			hasAudioB := formats[j].HasAudio
			audioB := 0
			if hasAudioB {
				audioB = 1
			}
			if audioA != audioB {
				return audioA > audioB
			}

			// 2. 编码优先级
			codecA := youtube.GetBaseCodec(formats[i].MimeType)
			codecB := youtube.GetBaseCodec(formats[j].MimeType)
			if codecA != codecB {
				return youtube.VideoCodecPriority[codecA] > youtube.VideoCodecPriority[codecB]
			}

			// 4. 文件大小
			return formats[i].Size > formats[j].Size
		})
		if len(formats) > 0 {
			result = append(result, formats[0])
		}
	}
	return result
}
