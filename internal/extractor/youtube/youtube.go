package youtube

import (
	"garden/internal/schema"
	"garden/internal/service"
	"github.com/samber/lo"
	"net/url"
	"sort"
	"strings"
)

const (
	MaxFileSize = 512 * 1024 * 1024 // 512MB
)

var (
	QualityOrder       = []int{4320, 2160, 1440, 1080, 720, 480, 360, 240, 144}
	VideoCodecPriority = map[string]int{
		"avc1":    5,
		"h264":    5,
		"h265":    4,
		"hevc":    4,
		"vp9":     3,
		"av01":    2,
		"unknown": 0,
	}
	AudioCodecPriority = map[string]int{
		"mp4a":    3,
		"opus":    2,
		"unknown": 0,
	}

	formats     = []string{"srt", "vtt", "ttml", "json3", "srv1", "srv2", "srv3"}
	GotoService *service.GotoService
)

func init() {
	aes, err := service.NewAES()
	if err != nil {
		panic(err)
	}
	GotoService = service.NewGotoService(aes, nil, nil)
}

type Thumbnail struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

func GetBaseCodec(mimeType string) string {
	codecs := strings.Split(mimeType, "codecs=")
	if len(codecs) > 1 {
		// 移除引号并分割编码
		codec := strings.Trim(codecs[1], "\"")
		codecParts := strings.Split(codec, ",")
		if len(codecParts) > 0 {
			// 提取视频编码的基础部分
			baseCodec := strings.Split(codecParts[0], ".")[0]
			return strings.ToLower(baseCodec)
		}
	}
	return "unknown"
}

func GetLargeThumbnailURL(thumbnails []Thumbnail) string {
	sort.Slice(thumbnails, func(i, j int) bool {
		return thumbnails[i].Height < thumbnails[j].Height
	})
	return lo.LastOrEmpty(thumbnails).URL
}

func MakeSubtitleURLs(rawURL string) []*schema.SubtitleURL {
	subtitleURLs := make([]*schema.SubtitleURL, 0)
	u, err := url.Parse(rawURL)
	if err != nil {
		return nil
	}
	queryParams := u.Query()
	for _, f := range formats {
		if queryParams.Has("fmt") {
			queryParams.Set("fmt", f)
			u.RawQuery = queryParams.Encode()
		} else {
			queryParams.Add("fmt", f)
			u.RawQuery = queryParams.Encode()
		}
		// 将替换后的 URL 添加到切片中
		subtitleURLs = append(subtitleURLs, &schema.SubtitleURL{
			URL:    u.String(),
			Format: f,
		})
	}
	return subtitleURLs
}
