package bilibili

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/partner/yijianjiexi"
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"regexp"
	"strings"

	"github.com/tidwall/gjson"
)

var regexProfile = regexp.MustCompile(`.+space\.bilibili\.com/(\d+)`)

// 提取播放列表
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	if strings.Contains(req.URL, "b23.tv/") || strings.Contains(req.URL, "bili2233.cn/") {
		req.URL = redirect.ResolveFinalURL(ctx, req.URL, &redirect.Options{
			UserAgent: useragent.RandomDesktop(),
		})
	}
	if strings.Contains(req.URL, "/bangumi/") {
		return ExtractBangumiPlaylist(ctx, req)
	}
	match := reCollection.FindStringSubmatch(req.URL)
	if match != nil {
		return BilibiliService.GetCollectionInfo(ctx, match[2])
	}

	if regexProfile.MatchString(req.URL) {
		return yijianjiexi.ExtractPlaylist(ctx, req)
	}
	var bvID, avID string
	match = reBv.FindStringSubmatch(req.URL)
	if match != nil {
		bvID = match[1]
	}
	match = reAv.FindStringSubmatch(req.URL)
	if match != nil {
		avID = match[1]
	}
	if avID == "" && bvID == "" {
		return nil, fmt.Errorf("parse bvid from url failed%s", req.URL)
	}
	params := map[string]string{
		"bvid": bvID,
		"aid":  avID,
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(map[string]string{
			"User-Agent": useragent.RandomDesktop(),
		}).
		SetQueryParams(params).
		Get(postURL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] twttrapi: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	switch result.Get("code").String() {
	case "-400", "-404", "62002":
		return nil, extract.ErrContentDeleted
	case "-403":
		return nil, extract.ErrRetryable
	}
	if result.Get("code").Int() != 0 || !result.Get("data.pages").Exists() {
		return nil, extract.ErrExtractFailed
	}
	data := result.Get("data")
	if data.Get("redirect_url").Exists() && strings.Contains(data.Get("redirect_url").String(), "/bangumi/") {
		return ExtractBangumiPlaylist(ctx, req)
	}
	if data.Get("season_id").Exists() {
		return BilibiliService.GetCollectionInfo(ctx, data.Get("season_id").String())
	} else {
		return pages(data)
	} // 当多P处理
}

func pages(data gjson.Result) (*schema.Playlist, error) {
	playlist := &schema.Playlist{}
	for _, item := range data.Get("pages").Array() {
		post := &schema.Post{
			ID:   item.Get("cid").Str,
			Text: item.Get("part").Str,
		}
		url := fmt.Sprintf("https://www.bilibili.com/video/%s?p=%s", data.Get("bvid").Str, item.Get("page").Str)
		resourceURL, err := GotoService.GenerateGotoURL(schema.Payload{"url": url}, false, nil)
		if err != nil {
			return nil, err
		}
		media := schema.NewVideoMedia(resourceURL, data.Get("pic").Str)
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}

	return playlist, nil
}

func GenerateGotoURL(s string) string {
	return s
}
