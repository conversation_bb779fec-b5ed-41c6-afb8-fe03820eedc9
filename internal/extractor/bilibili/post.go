package bilibili

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/partner/yijianjiexi"
	"garden/internal/schema"
	"garden/internal/service"
	"garden/internal/service/redirect"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"github.com/gongyinshi/shared/urlx"
	"regexp"
	"strings"

	"github.com/tidwall/gjson"
)

const postURL = "https://api.bilibili.com/x/web-interface/view"

var (
	headers = map[string]string{
		"origin":     "https://www.bilibili.com",
		"user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36",
	}
	reBv         = regexp.MustCompile(`.+bilibili\.com/.+/((bv|BV)\w+)`)
	reAv         = regexp.MustCompile(`.+bilibili\.com/.+/av(\d+)`)
	reCollection = regexp.MustCompile(`.+space.bilibili\.com/.+/(collectiondetail|seriesdetail)\?sid=(\d+)`)
	reProfile    = regexp.MustCompile(`.+space.bilibili\.com/(\d+)`)
)

// 单个帖子提取
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	if strings.Contains(req.URL, "b23.tv/") || strings.Contains(req.URL, "bili2233.cn/") {
		req.URL = redirect.ResolveFinalURL(ctx, req.URL, &redirect.Options{
			UserAgent: useragent.RandomDesktop(),
		})
	}
	if strings.Contains(req.URL, "/bangumi/") {
		return ExtractBangumi(ctx, req)
	}
	if reProfile.FindStringSubmatch(req.URL) != nil {
		return yijianjiexi.Extract(ctx, req)
	}
	var bvID, avID string
	match := reBv.FindStringSubmatch(req.URL)
	if match != nil {
		bvID = match[1]
	}
	match = reAv.FindStringSubmatch(req.URL)
	if match != nil {
		avID = match[1]
	}
	if avID == "" && bvID == "" {
		return nil, fmt.Errorf("parse bvid from url failed%s", req.URL)
	}
	params := map[string]string{
		"bvid": bvID,
		"aid":  avID,
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(map[string]string{
			"User-Agent": useragent.RandomDesktop(),
		}).
		SetQueryParams(params).
		Get(postURL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[post] bilibili: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	switch result.Get("code").String() {
	case "-400", "-404", "62002":
		return nil, extract.ErrContentDeleted
	case "-403":
		return nil, extract.ErrRetryable
	}
	if result.Get("code").Int() != 0 || !result.Get("data.pages").Exists() {
		return nil, extract.ErrExtractFailed
	}
	data := result.Get("data")
	if data.Get("redirect_url").Exists() && strings.Contains(data.Get("redirect_url").String(), "/bangumi/") {
		return ExtractBangumi(ctx, req)
	}
	p, err := urlx.GetQueryValue(req.URL, "p")
	if err != nil {
		return nil, err
	}
	pages := data.Get("pages").Array()
	pageMap := make(map[string]gjson.Result)

	for _, page := range pages {
		key := page.Get("page").String()
		pageMap[key] = page
	}
	key := p
	if key == "" {
		key = "1"
	}
	var page gjson.Result
	if res, ok := pageMap[key]; ok {
		page = res
	} else {
		page = pages[0]
	}
	cID := page.Get("cid").String()
	if cID == "" {
		return nil, extract.ErrExtractFailed
	}
	return service.GetAcgPlayInfo(ctx, data.Get("bvid").Str, cID, data.Get("pic").Str, page.Get("part").Str)
}
