package bilibili

import (
	"context"
	"encoding/json"
	"garden/internal/schema"
	"testing"
)

// UGC https://www.bilibili.com/video/BV1mKVizwEXF/
// UGC https://www.bilibili.com/video/BV1tH5jzVEGz?spm_id_from=333.788.videopod.sections
// 番剧 https://www.bilibili.com/bangumi/play/ep114966?from_spmid=666.4.feed.10
// 番剧 https://www.bilibili.com/bangumi/play/ss29310?from_spmid=666.25.recommend.0
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.bilibili.com/bangumi/play/ss29310?from_spmid=666.25.recommend.0",
	})
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(post, "", "  ")

	t.Log(string(b))
}

// https://www.bilibili.com/bangumi/play/ep67704?from_spmid=666.4.feed.24
// https://www.bilibili.com/bangumi/play/ss779?from_spmid=666.25.recommend.0
func TestExtractBangumi(t *testing.T) {
	bangumi, err := ExtractBangumi(context.Background(), &schema.ExtractReq{
		URL: "https://www.bilibili.com/bangumi/play/ss779?from_spmid=666.25.recommend.0",
	})
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(bangumi, "", "  ")

	t.Log(string(b))
}

// 主页 https://space.bilibili.com/10119428 TODO UID 需要补充跳转到 其他提取器的playlist
// 主页 https://space.bilibili.com/37029661 TODO UID 需要补充跳转到 其他提取器的playlist
// 合集 https://space.bilibili.com/37029661/lists/3018?type=season TODO UID 需要补充跳转到 其他提取器的playlist
// 合集 https://space.bilibili.com/37029661/lists/5222937?type=season TODO UID 需要补充跳转到 其他提取器的playlist

// collection: https://space.bilibili.com/10673533/channel/seriesdetail?sid=258433 // TODO 不支持
// collection: https://space.bilibili.com/16385920/channel/collectiondetail?sid=598302

// 番剧: https://www.bilibili.com/bangumi/play/ss12243
// 番剧: https://www.bilibili.com/bangumi/play/ss38158
// 番剧: https://www.bilibili.com/bangumi/play/ss41755

// 多P: https://www.bilibili.com/video/BV1mKVizwEXF/
func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://space.bilibili.com/10673533/channel/seriesdetail?sid=258433",
	})
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(playlist, "", "  ")
	t.Log(string(b))
}
