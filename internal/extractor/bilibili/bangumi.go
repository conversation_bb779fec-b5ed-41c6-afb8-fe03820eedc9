package bilibili

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service"
	"regexp"

	"github.com/tidwall/gjson"
)

var (
	reMD = regexp.MustCompile(`.+bilibili\.com/bangumi/media/md(\d+)`)
	reEP = regexp.MustCompile(`.+bilibili\.com/bangumi/play/ep(\d+)`)
	reSS = regexp.MustCompile(`.+bilibili\.com/bangumi/play/ss(\d+)`)
)

// 提取番剧
func ExtractBangumi(ctx context.Context, req *schema.ExtractReq) (post *schema.Post, err error) {
	var seasonID, episodeID string
	match := reMD.FindStringSubmatch(req.URL)
	epMatch := reEP.FindStringSubmatch(req.URL)
	ssMatch := reSS.FindStringSubmatch(req.URL)
	if match != nil {
		seasonID, err = getSeasonID(ctx, match[1])
		if err != nil {
			return nil, err
		}
	} else if epMatch != nil {
		episodeID = epMatch[1]
	} else if ssMatch != nil {
		seasonID = ssMatch[1]
	} else {
		return nil, fmt.Errorf("no matched bangumi")
	}
	params := map[string]string{
		"season_id": seasonID,
		"ep_id":     episodeID,
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(service.BilibiliHeaders).
		SetQueryParams(params).
		Get("https://api.bilibili.com/pgc/view/web/season")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[bangumi post] bilibili: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	episodes := result.Get("result.episodes").Array()
	if len(episodes) == 0 {
		return nil, fmt.Errorf("no episodes")
	}
	episode := episodes[0]
	for _, e := range episodes {
		if e.Get("id").Str == episodeID {
			episode = e
		}
	}
	return service.GetPgcPlayInfo(ctx, episode.Get("aid").String(), episode.Get("bvid").String(), episode.Get("cid").String(), episode.Get("id").String(), episode.Get("cover").String(), episode.Get("share_copy").String())
}

// 提取番剧playlist
func ExtractBangumiPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	var seasonID, episodeID string
	match := reMD.FindStringSubmatch(req.URL)
	epMatch := reEP.FindStringSubmatch(req.URL)
	ssMatch := reSS.FindStringSubmatch(req.URL)
	if match != nil {
		seasonID, err = getSeasonID(ctx, match[1])
		if err != nil {
			return nil, err
		}
	} else if epMatch != nil {
		episodeID = epMatch[1]
	} else if ssMatch != nil {
		seasonID = ssMatch[1]
	} else {
		return nil, fmt.Errorf("no matched bangumi")
	}
	params := map[string]string{
		"season_id": seasonID,
		"ep_id":     episodeID,
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(service.BilibiliHeaders).
		SetQueryParams(params).
		Get("https://api.bilibili.com/pgc/view/web/season")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[bangumi post] bilibili: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	episodes := result.Get("result.episodes").Array()
	if len(episodes) == 0 {
		return nil, fmt.Errorf("no episodes")
	}
	playlist = &schema.Playlist{}
	for _, e := range episodes {
		payload := schema.Payload{
			"aid":   e.Get("aid").Str,
			"bvid":  e.Get("bvid").Str,
			"cid":   e.Get("cid").Str,
			"ep_id": e.Get("id").Str,
		}
		resourceURL, err := GotoService.GenerateGotoURL(payload, false, nil)
		if err != nil {
			return nil, err
		}
		media := schema.NewVideoMedia(resourceURL, e.Get("cover").Str)
		post := &schema.Post{
			ID:         e.Get("id").Str,
			CreateTime: int(e.Get("pub_time").Int()),
			Text:       e.Get("share_copy").Str,
		}
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}
	playlist.User = &schema.User{
		Username: result.Get("result.season_title").Str,
		Avatar:   result.Get("result.square_cover").Str,
	}
	return playlist, nil
}

func getSeasonID(ctx context.Context, mediaID string) (string, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(service.BilibiliHeaders).
		Get(fmt.Sprintf("https://api.bilibili.com/pgc/review/user?media_id=%s", mediaID))
	if err != nil {
		return "", err
	}
	if resp.IsError() {
		return "", fmt.Errorf("get season id failed: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	return result.Get("result.media.season_id").Str, nil
}
