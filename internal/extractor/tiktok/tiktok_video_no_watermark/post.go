package tiktok_video_no_watermark

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"

	"github.com/samber/lo"
)

const (
	// https://rapidapi.com/ponds4552/apiPost/tiktok-best-experience/playground/endpoint_da5be938-7792-4fe4-9377-6e1e114d50b4
	postAPI = "https://tiktok-video-no-watermark2.p.rapidapi.com/"
)

var headers = map[string]string{
	"x-rapidapi-host": "tiktok-video-no-watermark2.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

type result struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		ID          string `json:"id"`
		Title       string `json:"title"`
		Hdplay      string `json:"hdplay"`
		Play        string `json:"play"`
		OriginCover string `json:"origin_cover"`
		Cover       string `json:"cover"`
		MusicInfo   struct {
			Play  string `json:"play"`
			Cover string `json:"cover"`
		} `json:"music_info"`
		Images []string `json:"images"`
	} `json:"data"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParams(map[string]string{
			"url": req.URL,
			"hd":  "1",
		}).
		SetHeaders(headers).
		SetResult(&result).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("tiktok_video_no_watermark: %s", resp.String())
	}
	if result.Code != 0 {
		return nil, fmt.Errorf("tiktok_video_no_watermark: %s", result.Msg)
	}

	data := result.Data
	post := &schema.Post{
		Text: data.Title,
	}
	// 视频
	videoURL := lo.CoalesceOrEmpty(data.Hdplay, data.Play)
	if videoURL != "" && videoURL != data.MusicInfo.Play {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, lo.CoalesceOrEmpty(data.OriginCover, data.Cover)))
	}
	// 图片
	for _, image := range data.Images {
		post.Medias = append(post.Medias, schema.NewImageMedia(image))
	}
	// 音乐
	if data.MusicInfo.Play != "" {
		post.Medias = append(post.Medias, schema.NewAudioMedia(data.MusicInfo.Play, data.MusicInfo.Cover))
	}
	return post, nil
}
