package tiktok_video_no_watermark

import (
	"context"
	"garden/internal/schema"
	"testing"
)

var unsupportedURL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
var videoURL = "https://www.tiktok.com/@unacrystal/video/7499466519894707474?q=gallery&t=1746278058292"
var imageURL = "https://www.tiktok.com/@galleryforall/photo/7245244726939585797"
var imagePlaylistURL = "https://www.tiktok.com/@galleryforall"

func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.tiktok.com/t/ZT2pqvGn9/",
		// URL: videoURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
