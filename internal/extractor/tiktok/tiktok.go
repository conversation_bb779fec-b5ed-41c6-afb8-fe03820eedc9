package tiktok

import (
	"context"
	"garden/internal/service/redirect"
	"garden/pkg/useragent"
	"regexp"
)

// https://www.tiktok.com/t/ZTjSoYTUo/
// https://www.tiktok.com/t/ZT2pqvGn9/
var shortURLRegex = regexp.MustCompile(`(?i)https?://(?:www\.)?tiktok\.com/t/([\w-]+)`)

// Unshorten 短链接转长链接
func Unshorten(ctx context.Context, originalURL string) string {
	// 判断rawURL是否是短链接
	if !shortURLRegex.MatchString(originalURL) {
		return originalURL
	}
	// 短链接转长链接
	return redirect.ResolveFinalURL(ctx, originalURL, &redirect.Options{
		UserAgent: useragent.RandomMobile(),
	})
}
