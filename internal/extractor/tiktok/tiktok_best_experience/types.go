package tiktok_best_experience

type AwemeDetail struct {
	AwemeID    string `json:"aweme_id"`
	CreateTime int    `json:"create_time"`
	Desc       string `json:"desc"`
	AwemeType  int    `json:"aweme_type"` // 该字段不准确，比如0和4都代表视频
	Video      struct {
		PlayAddr struct {
			URLList []string `json:"url_list"`
		} `json:"play_addr"`
		OriginCover struct {
			URLList []string `json:"url_list"`
		} `json:"origin_cover"`
	} `json:"video"`
	ImagePostInfo struct {
		Images []struct {
			Thumbnail struct {
				URLList []string `json:"url_list"`
			} `json:"thumbnail"`
		} `json:"images"`
	} `json:"image_post_info"`
	Music struct {
		PlayURL struct {
			URLList []string `json:"url_list"`
		} `json:"play_url"`
		CoverLarge struct {
			URLList []string `json:"url_list"`
		} `json:"cover_large"`
	} `json:"music"`
	Statistics struct {
		DiggCount    *int `json:"digg_count"`
		CommentCount *int `json:"comment_count"`
		ShareCount   *int `json:"share_count"`
		PlayCount    *int `json:"play_count"`
	} `json:"statistics"`
	Author struct {
		Nickname     string `json:"nickname"`
		AvatarLarger struct {
			URLList []string `json:"url_list"`
		} `json:"avatar_larger"`
	} `json:"author"`
	MixInfo struct { // 抖音合集信息
		MixName  string `json:"mix_name"`
		CoverURL struct {
			URLList []string `json:"url_list"`
		} `json:"cover_url"`
	} `json:"mix_info"`
}

type postResult struct {
	Status string `json:"status"`
	Error  string `json:"error"`
	Data   struct {
		AwemeDetail AwemeDetail `json:"aweme_detail"`
	} `json:"data"`
}

type feedResult struct {
	Status string `json:"status"`
	Error  string `json:"error"`
	Data   struct {
		Cursor    int           `json:"cursor"`     //  hashtag 标签页返回cursor
		MaxCursor int           `json:"max_cursor"` //  user 主页返回max_cursor
		HasMore   int           `json:"has_more"`   // 1: 有更多 0: 没有更多
		AwemeList []AwemeDetail `json:"aweme_list"`
	} `json:"data"`
}
