package tiktok_best_experience

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/tiktok"
	"garden/internal/schema"
	"garden/pkg/extract"
	"regexp"

	"github.com/samber/lo"
)

// https://rapidapi.com/ponds4552/postAPI/tiktok-best-experience/playground/endpoint_da5be938-7792-4fe4-9377-6e1e114d50b4
const (
	postAPI        = "https://tiktok-best-experience.p.rapidapi.com/video/%s"
	userFeedAPI    = "https://tiktok-best-experience.p.rapidapi.com/user/%s/feed"
	hashtagFeedAPI = "https://tiktok-best-experience.p.rapidapi.com/challenge/%s/feed"
)

var headers = map[string]string{
	"x-rapidapi-host": "tiktok-best-experience.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

// 使用正则表达式从字符串中提取19位的视频ID
var videoIDRegex = regexp.MustCompile(`\b\d{19}\b`)

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	req.URL = tiktok.Unshorten(ctx, req.URL)
	videoID := videoIDRegex.FindString(req.URL)
	if videoID == "" {
		return nil, extract.ErrUnsupportedURL
	}

	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetResult(&result).
		Get(fmt.Sprintf(postAPI, videoID))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("tiktok_best_experience: %s", resp.String())
	}
	if result.Status != "ok" {
		return nil, fmt.Errorf("tiktok_best_experience: %s", result.Error)
	}

	return convertToPost(result.Data.AwemeDetail), nil
}

func convertToPost(aweme AwemeDetail) *schema.Post {
	post := &schema.Post{
		Text: aweme.Desc,
	}
	videoURL := lo.FirstOrEmpty(aweme.Video.PlayAddr.URLList)
	audioURL := lo.FirstOrEmpty(aweme.Music.PlayURL.URLList)
	// 视频（非音乐）
	if videoURL != "" && videoURL != audioURL {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, lo.FirstOrEmpty(aweme.Video.OriginCover.URLList)))
	}
	// 图片
	for _, image := range aweme.ImagePostInfo.Images {
		if len(image.Thumbnail.URLList) > 0 {
			post.Medias = append(post.Medias, schema.NewImageMedia(image.Thumbnail.URLList[0]))
		}
	}
	// 音乐
	if audioURL != "" {
		post.Medias = append(post.Medias, schema.NewAudioMedia(audioURL, lo.FirstOrEmpty(aweme.Music.CoverLarge.URLList)))
	}
	return post
}
