package tiktok_best_experience

import (
	"context"
	"garden/internal/schema"
	"testing"
)

var unsupportedURL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
var videoURL = "https://www.tiktok.com/@unacrystal/video/7499466519894707474?q=gallery&t=1746278058292"
var imageURL = "https://www.tiktok.com/@galleryforall/photo/7245244726939585797"

var userImagesURL = "https://www.tiktok.com/@galleryforall"
var userURL1 = "https://www.tiktok.com/@user3963993816588"
var userURL2 = "https://www.tiktok.com/@thocungtroidat99966369"
var tagURL1 = "https://www.tiktok.com/tag/foryoupage%E2%9D%A4%EF%B8%8F%E2%9D%A4%EF%B8%8F"
var tagURL2 = "https://www.tiktok.com/tag/%E7%A4%BE%E4%BC%9A%E4%B8%BB%E4%B9%89%E5%9B%BD%E5%AE%B6"

// 短链接
var shortURL = "https://www.tiktok.com/t/ZT2pqvGn9/"

func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.tiktok.com/@unacrystal/video/7488466519894707474",
		// URL: videoURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}

func TestExtractPlaylist(t *testing.T) {
	result, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: shortURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
