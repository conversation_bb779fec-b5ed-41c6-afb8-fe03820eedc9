package tiktok_best_experience

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/tiktok"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	// 短链接转长链接
	req.URL = tiktok.Unshorten(ctx, req.URL)
	paths := urlx.ParsePath(req.URL)
	if len(paths) < 1 {
		return nil, extract.ErrInvalidPlaylistURL
	}
	var name, api string
	if paths[0] == "tag" && len(paths) > 1 {
		// hashtag 标签页
		name = paths[1]
		api = fmt.Sprintf(hashtagFeedAPI, name)
	} else if strings.HasPrefix(paths[0], "@") {
		// 用户主页
		name = paths[0][1:]
		api = fmt.Sprintf(userFeedAPI, name)
	} else {
		return nil, extract.ErrInvalidPlaylistURL
	}

	var result feedResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("max_cursor", req.Cursor).
		SetResult(&result).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] tiktok_best_experience: %s", resp.String())
	}
	if result.Status != "ok" {
		if result.Error == "user does not exist" || result.Error == "empty body" {
			return nil, extract.ErrUserNotFound
		}
		return nil, fmt.Errorf("[playlist] tiktok_best_experience: %s", result.Error)
	}

	playlist := &schema.Playlist{}
	data := result.Data
	if data.HasMore == 1 {
		playlist.NextCursor = strconv.Itoa(lo.CoalesceOrEmpty(data.MaxCursor, data.Cursor))
		playlist.HasMore = true
	}
	// 帖子列表
	playlist.Posts = ConvertToPosts(data.AwemeList)
	// 主页/播放列表信息
	playlist.User = &schema.User{
		Username: name,
		Avatar:   lo.FirstOrEmpty(lo.FirstOrEmpty(data.AwemeList).Author.AvatarLarger.URLList),
	}
	return playlist, nil
}

func ConvertToPosts(awemeList []AwemeDetail) []*schema.Post {
	posts := make([]*schema.Post, 0, len(awemeList))
	for _, aweme := range awemeList {
		post := convertToPost(aweme)
		post.ID = aweme.AwemeID
		post.CreateTime = aweme.CreateTime
		post.Stats = &schema.Stats{
			DiggCount:    aweme.Statistics.DiggCount,
			CommentCount: aweme.Statistics.CommentCount,
			ShareCount:   aweme.Statistics.ShareCount,
			PlayCount:    aweme.Statistics.PlayCount,
		}
		posts = append(posts, post)
	}
	return posts
}
