package ip13

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"

	"github.com/tidwall/gjson"
)

const api = "https://api.ip13.com/api/remark5/"

const apiKey = "Alu8K4jxUbkai8L3Rul1CofsQf" // <EMAIL>

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	return DoExtract(ctx, apiKey, req.URL)
}

func DoExtract(ctx context.Context, apiKey, url string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParam("key", apiKey).
		SetQueryParam("url", url).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("ip13: %s", resp.String())
	}

	// 使用gjson解析响应
	result := resp.String()
	if gjson.Get(result, "code").Int() != 200 {
		return nil, fmt.Errorf("ip13: %s", gjson.Get(result, "msg").String())
	}

	data := gjson.Get(result, "data")
	post := &schema.Post{
		Text: data.Get("title").String(),
	}

	// 视频
	videoURL := data.Get("videourl").String()
	if urlx.IsHTTPURL(videoURL) {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, data.Get("cover").String()))
	}

	// 图片
	data.Get("images").ForEach(func(_, value gjson.Result) bool {
		if urlx.IsHTTPURL(value.String()) {
			post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
		}
		return true // keep iterating
	})

	// 只有封面图
	if post.IsEmpty() && urlx.IsHTTPURL(data.Get("cover").String()) {
		post.Medias = append(post.Medias, schema.NewImageMedia(data.Get("cover").String()))
	}

	return post, nil
}
