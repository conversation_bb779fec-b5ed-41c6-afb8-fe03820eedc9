package guijianpan

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"
)

const api = "https://api.guijianpan.com/waterRemoveDetail/xxmQsyByAk?ak=ba779222f59a4196bd8313978c3513eb"

type result struct {
	Code    string `json:"code"` // “10000” 代表成功
	Msg     string `json:"msg"`
	Content struct {
		Author    string     `json:"author"`
		Avatar    string     `json:"avatar"`
		Cover     string     `json:"cover"` // 视频封面
		CoverList []any      `json:"coverList"`
		ImageList []struct { // 图集地址
			URL string `json:"url"`
		} `json:"imageList"`
		Title     string `json:"title"`
		Type      string `json:"type"` // "IMAGE"、“VIDEO”
		URL       string `json:"url"`  // 视频地址
		VideoList []any  `json:"videoList"`
	} `json:"content"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParam("link", req.URL).
		SetResult(&result).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("guijianpan: %s", resp.String())
	}
	if result.Code != "10000" {
		return nil, fmt.Errorf("guijianpan: %s", result.Msg)
	}
	post := &schema.Post{
		Text: result.Content.Title,
	}
	// 视频
	if urlx.IsHTTPURL(result.Content.URL) {
		post.Medias = append(post.Medias, schema.NewVideoMedia(result.Content.URL, result.Content.Cover))
	}
	// 图片
	for _, image := range result.Content.ImageList {
		if urlx.IsHTTPURL(image.URL) {
			post.Medias = append(post.Medias, schema.NewImageMedia(image.URL))
		}
	}
	// 只有封面图
	if post.IsEmpty() && urlx.IsHTTPURL(result.Content.Cover) {
		post.Medias = append(post.Medias, schema.NewImageMedia(result.Content.Cover))
	}
	return post, nil
}
