package guijianpan

import (
	"context"
	"garden/internal/schema"
	"testing"
)

var unsupportedURL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
var videoURL = "https://www.xiaohongshu.com/explore/6843a055000000002100b41f?xsec_token=ABNelfnwNgZ14PxStMHKfOOIz4eV8Q84ZOLFV57q6Mg3U=&xsec_source=pc_feed"
var imageURL = "https://www.xiaohongshu.com/explore/685bb317000000002203c558?xsec_token=ABJpSErD2I2PZi9ENJkp7EpSmEo2aiox1zDJiVsozcHfY=&xsec_source=pc_feed"

func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: imageURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
