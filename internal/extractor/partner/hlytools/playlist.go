package hlytools

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"log/slog"

	"github.com/tidwall/gjson"
)

const playlistAPI = "https://watermark-api.hlyphp.top/AuthorWork/Index"
const appID = "6884ea359d4f72171HOfpz"

// 支持抖音、快手
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	params := map[string]string{
		"appid": appID,
		"link":  req.URL,
	}
	if req.Cursor != "" {
		params["max_cursor"] = req.Cursor
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParams(params).
		Get(playlistAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("hlytools: %s", resp.String())
	}

	playlist := &schema.Playlist{}
	result := resp.String()
	if gjson.Get(result, "code").Int() != 1 { // 1 表示成功
		slog.Warn("[hlytools] 播放列表提取失败", slog.Any("req", req), slog.Any("result", result))
		return playlist, nil
	}

	data := gjson.Get(result, "data")
	if maxCursor := data.Get("max_cursor").String(); maxCursor != "" {
		playlist.HasMore = true
		playlist.NextCursor = maxCursor
	}

	// 帖子列表
	data.Get("aweme_list").ForEach(func(key, item gjson.Result) bool {
		post := &schema.Post{
			Text: item.Get("title").String(),
			ID:   item.Get("aweme_id").String(), // 视频ID
		}

		// 视频
		if videoURL := item.Get("videoSrc").String(); videoURL != "" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, item.Get("imageSrc").String()))
		}

		// 音频
		if audioURL := item.Get("music").String(); audioURL != "" {
			post.Medias = append(post.Medias, schema.NewAudioMedia(audioURL, ""))
		}

		// 图集
		item.Get("imageAtlas").ForEach(func(key, value gjson.Result) bool {
			post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
			return true
		})

		if post.IsEmpty() {
			return true
		}

		playlist.Posts = append(playlist.Posts, post)
		return true
	})

	playlist.User = &schema.User{
		Username: data.Get("author.nickname").String(),
		Avatar:   data.Get("author.avatar").String(),
	}
	return playlist, nil
}
