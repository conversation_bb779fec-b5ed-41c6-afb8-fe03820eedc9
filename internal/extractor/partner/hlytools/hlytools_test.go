package hlytools

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// var unsupportedURL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
// var videoURL = "https://www.xiaohongshu.com/explore/67f21703000000001202dd4a?xsec_token=ABkvYWTpHsC594QtbFKCGKo-Al-yPvSIa5uWMRa9gGaN0=&xsec_source=pc_feed"
// var imageURL = "https://www.xiaohongshu.com/explore/685df4c5000000002203071a?xsec_token=AB-qUz0I39Uue6ieH3H-pNyniIsMXXpHqjIVP5Ec2CbkA=&xsec_source=pc_feed"

// func TestExtract(t *testing.T) {
// 	result, err := Extract(context.Background(), &schema.ExtractReq{
// 		URL: imageURL,
// 	})
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	t.Log(result)
// }

// 抖音主页 视频  https://www.douyin.com/user/MS4wLjABAAAAuKGEpFTCOSfIMYPvlSXLdeMaI-1GEURntUKpenDsyn8?from_tab_name=main
// 抖音主页 图集  https://www.douyin.com/user/MS4wLjABAAAAWaGilNIaG7Py--QNw3lwuFhMAQwUIq0QE-rdVlEliFA

// 快手主页 视频  https://www.kuaishou.com/profile/3xmbqbs8qsw7fn4
func TestExtractPlaylist(t *testing.T) {
	result, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL:    "https://www.kuaishou.com/profile/3xmbqbs8qsw7fn4",
		Cursor: "1752807660000",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
