package ake999

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/urlx"

	"github.com/tidwall/gjson"
)

const api = "https://api.ake999.com/api/dsp/32A8B0C13238D404353E875C72BF1FD29492B17A270E8CFD90/202037645/"

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	return DoExtract(ctx, api, req.URL)
}

func DoExtract(ctx context.Context, api, url string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParam("url", url).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("ake999: %s", resp.String())
	}

	result := resp.String()
	if gjson.Get(result, "code").String() != "200" {
		return nil, fmt.Errorf("ake999: %s", gjson.Get(result, "msg").String())
	}

	data := gjson.Get(result, "data")
	post := &schema.Post{
		Text: data.Get("title").String(),
	}

	// 视频
	videoURL := data.Get("url").String()
	coverURL := data.Get("cover").String()
	if urlx.IsHTTPURL(videoURL) {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, coverURL))
	}

	// 图片
	data.Get("images").ForEach(func(_, value gjson.Result) bool {
		if urlx.IsHTTPURL(value.String()) {
			post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
		}
		return true // keep iterating
	})

	// 只有封面图
	if post.IsEmpty() && urlx.IsHTTPURL(coverURL) {
		post.Medias = append(post.Medias, schema.NewImageMedia(coverURL))
	}

	return post, nil
}
