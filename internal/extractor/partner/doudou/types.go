package doudou

type DouDouResult struct {
	Status int    `json:"status"`
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Data   struct {
		Title         string   `json:"title"`
		Pics          []string `json:"pics"`
		UserID        string   `json:"userID"`
		URL           string   `json:"url"`
		Cover         string   `json:"cover"`
		MusicURL      string   `json:"musicUrl"`
		Type          string   `json:"type"`
		BigFile       bool     `json:"bigFile"`
		Yurl          string   `json:"yurl"`
		Bs            string   `json:"bs"`
		Down          string   `json:"down"`
		DownloadImage string   `json:"download_image"`
	} `json:"data"`
}
