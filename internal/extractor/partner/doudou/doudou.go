package doudou

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
)

const postAPI = "https://jk.y-l.cc/api/dsp/328FAC692B72D955C10A2CFEC7AEA5DA9BD0AAEB58DE7AC402/202037220"

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	var result DouDouResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetBody(map[string]string{
			"url": req.URL,
		}).
		SetResult(&result).
		Post(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.Is<PERSON>rror() {
		return nil, fmt.Errorf("doudou: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("doudou: %s", result.Msg)
	}
	post := &schema.Post{
		Text: result.Data.Title,
	}
	for _, pic := range result.Data.Pics {
		post.Medias = append(post.Medias, schema.NewImageMedia(pic))
	}
	if result.Data.URL != "" {
		post.Medias = append(post.Medias, schema.NewVideoMedia(result.Data.URL, result.Data.Cover))
	}
	if result.Data.MusicURL != "" {
		post.Medias = append(post.Medias, schema.NewAudioMedia(result.Data.MusicURL, result.Data.Cover))
	}
	return post, nil
}
