package doudou

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://v.kuaishou.com/zmVX0s
// https://v.douyin.com/MGkSpJS/
// https://www.bilibili.com/video/BV1UD4y1b7eo/

// https://video.weibo.com/show?fid=1034:4757578882941075
// https://weibo.com/detail/4830591038789274
// https://weibo.com/1900408801/5193417988246947   有水印
// https://h5.pipigx.com/pp/post/************

// https://show.meitu.com/detail?feed_id=6992680999973844232

// https://www.xiaohongshu.com/explore/684fe81400000000210081b0?xsec_token=ABApcpHLVAwTcmASprtuSviawPMWw-1oT__to8-FgfYYY=&xsec_source=pc_feed

// 以下提取失败
// https://www.ixigua.com/7088617365683831332

// 以下不支持
// https://weibo.com/detail/4830591038789274
// https://kandianshare.html5.qq.com/v3/video/1714860247509472634?vid=1714860247509472634&sUserId=&sGuid=3263633433636663393733636564303966633336356238633063636338386362&classify=1&target_app=kb&classify=1&from_app=qb&src_app=1
// https://mparticle.uc.cn/video.html?uc_param_str=frdnsnpfvecpntnwprdsssnikt&from=msg#!wm_aid=d61582f66f944b4fb0d77f3a73962545!!wm_id=17284dcd0bd440a580125c79ea93efa1
// https://fn.music.163.com/g/mlog/mlog-mobile/landing/mlog?id=a1jBw5PjkhlA8Xe&type=2&userid=8095648785&songId=null&startTime=nu

// https://www.xiaohongshu.com/explore/6870e3b60000000022032b32?xsec_token=ABLw4lTckPAYR7XFEP0AauPOB_BVbl4aqF7NJZebdGiAA=&xsec_source=pc_feed
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://show.meitu.com/detail?feed_id=6992680999973844232",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
