package diadi

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://v.kuaishou.com/zmVX0s
// https://v.douyin.com/MGkSpJS/
// https://www.bilibili.com/video/BV1UD4y1b7eo/
// https://video.weibo.com/show?fid=1034:4757578882941075
// https://weibo.com/1900408801/5193417988246947   有水印

// 以下提取失败
// https://www.ixigua.com/7088617365683831332
// https://www.xiaohongshu.com/explore/684fe81400000000210081b0?xsec_token=ABApcpHLVAwTcmASprtuSviawPMWw-1oT__to8-FgfYYY=&xsec_source=pc_feed
// https://h5.pipigx.com/pp/post/************

// 以下不支持
// https://weibo.com/detail/4830591038789274
// https://kandianshare.html5.qq.com/v3/video/1714860247509472634?vid=1714860247509472634&sUserId=&sGuid=3263633433636663393733636564303966633336356238633063636338386362&classify=1&target_app=kb&classify=1&from_app=qb&src_app=1
// https://mparticle.uc.cn/video.html?uc_param_str=frdnsnpfvecpntnwprdsssnikt&from=msg#!wm_aid=d61582f66f944b4fb0d77f3a73962545!!wm_id=17284dcd0bd440a580125c79ea93efa1
// https://fn.music.163.com/g/mlog/mlog-mobile/landing/mlog?id=a1jBw5PjkhlA8Xe&type=2&userid=8095648785&songId=null&startTime=nu
func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://fn.music.163.com/g/mlog/mlog-mobile/landing/mlog?id=a1jBw5PjkhlA8Xe&type=2&userid=8095648785&songId=null&startTime=nu",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}

// https://www.douyin.com/user/MS4wLjABAAAAWaGilNIaG7Py--QNw3lwuFhMAQwUIq0QE-rdVlEliFA 失败
// 快手主页 视频  https://www.kuaishou.com/profile/3xmbqbs8qsw7fn4 失败
func TestExtractPlaylist(t *testing.T) {
	result, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://space.bilibili.com/37029661?spm_id_from=333.1007.tianma.1-1-1.click",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
