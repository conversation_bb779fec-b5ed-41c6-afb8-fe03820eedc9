package diadi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
)

// TODO 暂未调通
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	params := map[string]string{
		"url":        req.URL,
		"app_secret": appSecret,
		// "number": "10", // 最大支持30
	}
	if req.Cursor != "" {
		params["max_cursor"] = req.Cursor
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParams(params).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("hlytools: %s", resp.String())
	}
	return nil, nil
}
