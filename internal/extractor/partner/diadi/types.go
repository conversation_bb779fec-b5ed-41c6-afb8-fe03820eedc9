package diadi

type DiaDiPostResult struct {
	Code int `json:"code"`
	Data struct {
		Video    []string `json:"video"`
		Images   []string `json:"images"`
		Cover    string   `json:"cover"`
		Type     string   `json:"type"`
		Title    string   `json:"title"`
		Text     string   `json:"text"`
		Audio    []string `json:"audio"`
		Platform int      `json:"platform"`
	} `json:"data"`
	Message string `json:"message"`
}
