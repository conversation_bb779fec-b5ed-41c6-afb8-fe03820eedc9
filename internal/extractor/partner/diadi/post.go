package diadi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"

	"github.com/samber/lo"
)

const api = "https://gateway.diadi.cn/api/parse"
const appSecret = "3rLRUjsSqKylVLsHmbXFDHTxCoQvWzZaB"

// Bilibili、抖音、快手、
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	params := map[string]string{
		"url":        req.URL,
		"app_secret": appSecret,
		// "raw":"0" // 是否为永久链接 0否1是 默认0 platform=1时有效
		// "is_title":"0" // 是否区分标题和文案 0否1是 默认0 platform=9时有效
	}
	var result DiaDiPostResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParams(params).
		SetResult(&result).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("diadi: %s", resp.String())
	}
	if result.Code != 0 {
		return nil, fmt.Errorf("diadi: %s", result.Message)
	}
	post := &schema.Post{
		Text: lo.CoalesceOrEmpty(result.Data.Text, result.Data.Title),
	}
	for _, image := range result.Data.Images {
		post.Medias = append(post.Medias, schema.NewImageMedia(image))
	}
	for _, video := range result.Data.Video {
		post.Medias = append(post.Medias, schema.NewVideoMedia(video, result.Data.Cover))
	}
	for _, audio := range result.Data.Audio {
		post.Medias = append(post.Medias, schema.NewAudioMedia(audio, result.Data.Cover))
	}
	return post, nil
}
