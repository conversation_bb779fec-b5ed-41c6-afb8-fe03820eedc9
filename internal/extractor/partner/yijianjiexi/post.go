package yijianjiexi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/sites"
	"github.com/gongyinshi/shared/urlx"
	"strings"

	"github.com/tidwall/gjson"
)

const api = "http://api.yijianjiexi.com/api/v1/28077df42be4302e31228ea9b9b37c65"

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	site := sites.GetByURL(req.URL)
	if site == sites.BILIBILI && strings.Contains(req.URL, "/bangumi/") {
		// TODO: 提取番剧
		return nil, nil
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetFormData(map[string]string{
			"link": req.URL,
		}).
		Post(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("yijianjiexi: %s", resp.String())
	}

	result := resp.String()
	if gjson.Get(result, "code").String() != "200" {
		return nil, fmt.Errorf("yijianjiexi: %s", gjson.Get(result, "msg").String())
	}

	data := gjson.Get(result, "data")
	post := &schema.Post{
		Text: data.Get("desc").String(),
	}
	// 视频
	videoURL := data.Get("video_url").String()
	coverURL := data.Get("cover_url").String()
	if urlx.IsHTTPURL(videoURL) {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, coverURL))
	}
	// 图片
	data.Get("images").ForEach(func(_, value gjson.Result) bool {
		if urlx.IsHTTPURL(value.String()) {
			post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
		}
		return true
	})
	// 多个视频
	data.Get("videos").ForEach(func(_, value gjson.Result) bool {
		videoURL := value.Get("url").String()
		if urlx.IsHTTPURL(videoURL) {
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, ""))
		}
		return true
	})
	// 只有封面图
	if post.IsEmpty() && urlx.IsHTTPURL(coverURL) {
		post.Medias = append(post.Medias, schema.NewImageMedia(coverURL))
	}
	return post, nil
}
