package yijianjiexi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service"
	"log/slog"
	"strings"

	"github.com/tidwall/gjson"
)

const playlistAPI = "http://47.95.204.89/api/video_list/2031086511_cfb1cc191fa6df95.php"

var gotoService *service.GotoService

func init() {
	aes, err := service.NewAES()
	if err != nil {
		panic(err)
	}
	gotoService = service.NewGotoService(aes, nil, nil)
}

func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	// TODO: 短链接转长链接

	// TODO: 判断是否是合集

	params := map[string]string{"link": req.URL}
	if req.Cursor != "" {
		params["cursor"] = req.Cursor
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetFormData(params).
		Post(playlistAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("yijianjiexi: %s", resp.String())
	}

	playlist := &schema.Playlist{}
	result := resp.String()
	if code := gjson.Get(result, "code").String(); code != "200" && code != "0" { // 200 或者 0 表示成功
		slog.Warn("[yijianjiexi] 播放列表提取失败", slog.Any("req", req), slog.Any("result", result))
		return playlist, nil
	}

	data := gjson.Get(result, "data")
	if data.Get("has_more").Bool() {
		playlist.HasMore = true
		playlist.NextCursor = data.Get("cursor").String()
	}

	// 帖子列表
	items := data.Get("video_list").Array()
	if len(items) == 0 {
		items = data.Get("feeds").Array()
	}
	if len(items) == 0 {
		return playlist, nil
	}

	for _, item := range items {
		post := &schema.Post{
			Text: item.Get("desc").String(),
		}
		// 1 视频 2 图集
		if item.Get("type").Int() == 2 {
			item.Get("images").ForEach(func(key, value gjson.Result) bool {
				post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
				return true
			})
			if len(post.Medias) == 0 {
				if coverURL := item.Get("cover_url").String(); coverURL != "" {
					post.Medias = append(post.Medias, schema.NewImageMedia(coverURL))
				}
			}
		} else {
			videoURL := item.Get("video_url").String()
			if strings.Contains(videoURL, "yijianjiexi") {
				videoURL, err = gotoService.GenerateGotoURL(schema.Payload{
					"url": videoURL,
				}, true, nil)
				if err != nil {
					return nil, err
				}
			}
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, item.Get("cover_url").String()))
		}
		playlist.Posts = append(playlist.Posts, post)
	}

	author := data.Get("author")
	playlist.User = &schema.User{
		Username: author.Get("nickname").String(),
		Avatar:   author.Get("avatar").String(),
	}
	return playlist, nil
}
