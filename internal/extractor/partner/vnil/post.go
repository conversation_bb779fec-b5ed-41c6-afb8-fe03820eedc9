package vnil

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"github.com/gongyinshi/shared/sites"
	"github.com/gongyinshi/shared/urlx"
	"strings"

	"github.com/samber/lo"
)

const (
	postAPI           = "http://cusa.vnil.cn/api/parse/deal"
	appKey15989338790 = ""
	appKeyQsy193c     = ""
)

var appKeys = []string{appKey15989338790, appKeyQsy193c}

func randomAppKey() string {
	return lo.Sample(appKeys)
}

func randomAppKeyExclude(exclude string) string {
	return lo.Sample(lo.Filter(appKeys, func(key string, _ int) bool {
		return key != exclude
	}))
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	site := sites.GetByURL(req.URL)
	if site == sites.BILIBILI && strings.Contains(req.URL, "/bangumi/") {
		// TODO: 提取番剧
		return nil, nil
	}

	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetFormData(map[string]string{
			"url":    req.URL,
			"appkey": randomAppKey(),
		}).
		SetResult(&result).
		Post(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("vnil: %s", resp.String())
	}
	if result.Code != 0 { // 10009: request times limit
		return nil, fmt.Errorf("vnil: %s", result.Msg)
	}

	body := result.Body.Value
	post := &schema.Post{
		Text: body.Text,
	}
	// 视频
	if urlx.IsHTTPURL(body.VideoInfo.Value.VideoURL) {
		post.Medias = append(post.Medias, schema.NewVideoMedia(body.VideoInfo.Value.VideoURL, body.VideoInfo.Value.Cover))
	}
	// 图片
	for _, image := range body.Images {
		if urlx.IsHTTPURL(image) {
			post.Medias = append(post.Medias, schema.NewImageMedia(image))
		}
	}
	// 只有封面图
	if post.IsEmpty() && urlx.IsHTTPURL(body.Cover) {
		post.Medias = append(post.Medias, schema.NewImageMedia(body.Cover))
	}
	return post, nil
}
