package vnil

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"log/slog"
	"net/http"
	"time"

	"github.com/samber/lo"
)

const (
	authorInfoAPI = "http://cusa.vnil.cn/api/customparse/getInfo"
	userFeedAPI   = "http://cusa.vnil.cn/api/customparse/getList"
)

var gotoService *service.GotoService

func init() {
	aes, err := service.NewAES()
	if err != nil {
		panic(err)
	}
	gotoService = service.NewGotoService(aes, nil, nil)
}

// 多平台批量: 抖音、西瓜、頭条、微视、全民小视频、B站、火山、美拍、好看、绿洲、腾讯新闻、快报、轻视频、避风、QQ浏览器、UC浏览器
// https://huoguo.qq.com/m/person.html?userid=14110503&ptag=huoguo&first=1&share_uin=0
// http://a.mp.uc.cn/media.html?mid=fc6a303138e840df8c089d28eb046518&client=ucweb&uc_param_str=frdnsnpfvecpntnwprdsssnikt&uc_biz_str=S:custom%7CC:iflow_ncmt
// https://quanmin.hao222.com/feedvideoui/api?pd=author_share_mvideo&ucenter=ext%3D%257B%2522metiaId%2522%253A%2522H_LXnO9IOC95VBpAfH1siA%2522%252C%2522authorType%2522%253A%2522ugc%2522%252C%2522authorId%2522%253A%2522H_LXnO9IOC95VBpAfH1siA%2522%257D
// https://haokan.baidu.com/author/1633656498916868
// https://www.acfun.cn/u/19372766
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	// TODO: 短链接转长链接

	// TODO: 判断是否是合集

	author, err := getAuthor(ctx, req.URL)
	if err != nil {
		return nil, err
	}

	params := map[string]string{
		"uid":      author.Author.UID.String(),
		"platform": author.Platform,
		"appkey":   randomAppKey(),
		"cursor":   req.Cursor,
	}
	var result playlistResult
	resp, err := constant.RestyClient.R().SetContext(ctx).SetTimeout(20 * time.Second).
		SetFormData(params).
		SetResult(&result).
		Post(userFeedAPI)
	// 403表示频率限制，用另外一个appkey尝试
	if resp.StatusCode() == http.StatusForbidden { // 当接口返回err时，StatusCode()为0
		params["appkey"] = randomAppKeyExclude(params["appkey"])
		resp, err = constant.RestyClient.R().SetContext(ctx).SetTimeout(20 * time.Second).
			SetFormData(params).
			SetResult(&result).
			Post(userFeedAPI)
	}
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusForbidden {
			slog.Error("[vnil] playlist 第2个key仍返回403", slog.Any("req", req)) // 告警
		}
		return nil, fmt.Errorf("[vnil] playlist: %s", resp.String())
	}
	if result.Code != 0 {
		slog.Error("[vnil] playlist", slog.Any("req", req), slog.Any("result", result)) // TODO: 细化
		return nil, extract.ErrRetryable
	}

	playlist := &schema.Playlist{}
	if result.Body.Page.HasMore {
		playlist.HasMore = true
		playlist.NextCursor = result.Body.Page.NextCursor
	}
	for _, item := range result.Body.List {
		post := &schema.Post{
			Text:       item.Desc,
			ID:         lo.CoalesceOrEmpty(item.AwemeID, item.Vid),
			CreateTime: item.CreateTime,
		}
		// 视频
		if urlx.IsHTTPURL(item.VideoInfo.Value.VideoURL) {
			resourceURL := item.VideoInfo.Value.VideoURL
			if item.ToParse == 1 { // to_parse == 1 的情况
				resourceURL, err = gotoService.GenerateGotoURL(schema.Payload{"url": item.ShareURL}, false, nil)
				if err != nil {
					return nil, err
				}
			}
			post.Medias = append(post.Medias, schema.NewVideoMedia(resourceURL, item.VideoInfo.Value.Cover))
		}

		// 图片
		for _, image := range item.Images {
			if urlx.IsHTTPURL(image) {
				post.Medias = append(post.Medias, schema.NewImageMedia(image))
			}
		}
		post.Stats = &schema.Stats{
			DiggCount:    &item.LikeCount,
			CommentCount: &item.CommentCount,
			ShareCount:   &item.ShareCount,
		}
		playlist.Posts = append(playlist.Posts, post)
	}
	playlist.User = &schema.User{
		Username: author.Author.Name,
		Avatar:   author.Author.Avatar,
	}
	return playlist, nil
}

// 获取作者信息  TODO: redis缓存
func getAuthor(ctx context.Context, url string) (*authorBody, error) {
	var result authorResult
	params := map[string]string{
		"url":    url,
		"appkey": randomAppKey(),
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetFormData(params).
		SetResult(&result).
		Post(authorInfoAPI)
	// 403表示频率限制，用另外一个appkey尝试
	if resp.StatusCode() == http.StatusForbidden { // 当接口返回err时，StatusCode()为0
		params["appkey"] = randomAppKeyExclude(params["appkey"])
		resp, err = constant.RestyClient.R().SetContext(ctx).
			SetFormData(params).
			SetResult(&result).
			Post(authorInfoAPI)
	}
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[vnil] getAuthorInfo: %s", resp.String())
	}
	body := result.Body.Value
	if result.Code != 0 || body.Author.UID == "" {
		return nil, extract.ErrInvalidPlaylistURL
	}
	return &body, nil
}
