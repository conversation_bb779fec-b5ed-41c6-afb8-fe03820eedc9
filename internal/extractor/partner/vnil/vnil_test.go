package vnil

import (
	"context"
	"garden/internal/schema"
	"testing"
)

var unsupportedURL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
var videoURL = "https://www.xiaohongshu.com/explore/67f21703000000001202dd4a?xsec_token=ABkvYWTpHsC594QtbFKCGKo-Al-yPvSIa5uWMRa9gGaN0=&xsec_source=pc_feed"
var imageURL = "https://www.xiaohongshu.com/explore/680af719000000000b014103?xsec_token=ABBmmZu5ILCtt_R7cSYgZvzJuADEgqPW96fnZI1_JWVZw=&xsec_source=pc_feed"

func TestExtract(t *testing.T) {
	result, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.douyu.com/topic/2025KPL_SPRING?rid=36252&dyshid=0-",
		// URL: imageURL,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}

// 抖音主页 https://www.douyin.com/user/MS4wLjABAAAAuKGEpFTCOSfIMYPvlSXLdeMaI-1GEURntUKpenDsyn8?from_tab_name=main
// https://huoguo.qq.com/m/person.html?userid=14110503&ptag=huoguo&first=1&share_uin=0
// http://a.mp.uc.cn/media.html?mid=fc6a303138e840df8c089d28eb046518&client=ucweb&uc_param_str=frdnsnpfvecpntnwprdsssnikt&uc_biz_str=S:custom%7CC:iflow_ncmt
// https://quanmin.hao222.com/feedvideoui/api?pd=author_share_mvideo&ucenter=ext%3D%257B%2522metiaId%2522%253A%2522H_LXnO9IOC95VBpAfH1siA%2522%252C%2522authorType%2522%253A%2522ugc%2522%252C%2522authorId%2522%253A%2522H_LXnO9IOC95VBpAfH1siA%2522%257D
// https://haokan.baidu.com/author/1633656498916868
// https://www.acfun.cn/u/19372766
func TestGetAuthorInfo(t *testing.T) {
	info, err := getAuthor(context.Background(), "https://www.acfun.cn/u/19372766")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(info)
}

func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://www.douyin.com/user/MS4wLjABAAAAZ7T3lq8EXUTRkdSIaJSxY32ZJbHlUgPjI_Oi0C7oJxtMsEiKz6uSPQlwVQt-POVe",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(playlist)
}

func TestRandomAppKeyExclude(t *testing.T) {
	key := randomAppKey()
	t.Log(key)
}
