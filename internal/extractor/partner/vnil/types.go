package vnil

import "github.com/gongyinshi/shared/jsonx"

type postResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	// 当某项的数据空时，接口返回的obj数据是空数组，不符合规范
	Body jsonx.FlexibleObject[struct {
		Text      string    `json:"text"`
		VideoInfo videoInfo `json:"video_info"`
		Images    []string  `json:"images"`
		Cover     string    `json:"cover"`
		Type      int       `json:"type"` // 1:图集  2:视频
	}] `json:"body"`
}

type videoInfo jsonx.FlexibleObject[struct {
	Cover    string `json:"cover"`
	VideoURL string `json:"video_url"`
}]

type playlistResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Body struct {
		Page struct {
			NextCursor string `json:"next_cursor"`
			HasMore    bool   `json:"has_more"`
		} `json:"page"`
		List []struct {
			Vid          string    `json:"vid"`
			AwemeID      string    `json:"aweme_id"`
			VideoInfo    videoInfo `json:"video_info"`
			Images       []string  `json:"images"`
			Desc         string    `json:"desc"`
			ShareURL     string    `json:"share_url"`
			CreateTime   int       `json:"create_time"`
			LikeCount    int       `json:"like_count"`
			CommentCount int       `json:"comment_count"`
			ShareCount   int       `json:"share_count"`
			ToParse      int       `json:"to_parse"`
		} `json:"list"`
	} `json:"body"`
}

type authorBody struct {
	Platform string `json:"platform"`
	Author   struct {
		UID    jsonx.FlexibleString `json:"uid"`
		Name   string               `json:"name"`
		Avatar string               `json:"avatar"`
	} `json:"author"`
}

type authorResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	// 当提取失败时，接口返回的body是空数组，不符合规范
	Body jsonx.FlexibleObject[authorBody] `json:"body"`
}
