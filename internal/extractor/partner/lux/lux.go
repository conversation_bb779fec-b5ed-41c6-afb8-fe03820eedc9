package lux

import (
	"context"
	"fmt"
	"garden/internal/schema"
	"garden/pkg/media"

	_ "github.com/iawia002/lux/app" // 注册extractors
	"github.com/iawia002/lux/extractors"
	"github.com/samber/lo"
)

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	data, err := extractors.Extract(req.URL, extractors.Options{})
	if err != nil {
		return nil, fmt.Errorf("lux: %w", err)
	}
	if len(data) == 0 || len(data[0].Streams) == 0 {
		return nil, fmt.Errorf("lux: no data")
	}
	result := data[0]

	var stream *extractors.Stream
	for _, s := range result.Streams {
		if stream == nil || s.Size > stream.Size {
			stream = s
		}
	}

	post := &schema.Post{
		Text: result.Title,
	}
	for _, part := range stream.Parts {
		post.Medias = append(post.Medias, &schema.Media{
			MediaType:   lo.CoalesceOrEmpty(media.TypeByExtension(part.Ext), media.TypeVideo),
			ResourceURL: part.URL,
		})
	}
	return post, nil
}
