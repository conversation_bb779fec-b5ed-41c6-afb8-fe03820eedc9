package extractor

import (
	"context"
	"fmt"
	"garden/internal/schema"
)

type ExtractFunc func(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error)

type ExtractPlaylistFunc func(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error)

type ExtractSubtitlesFunc func(ctx context.Context, req *schema.ExtractReq) (*schema.SubtitlesResp, error)

var extractors = make(map[string]ExtractFunc)
var playlistExtractors = make(map[string]ExtractPlaylistFunc)
var subtitlesExtractors = make(map[string]ExtractSubtitlesFunc)

func GetAllExtractor() []string {
	var keys []string
	for k := range extractors {
		keys = append(keys, k)
	}
	for k := range playlistExtractors {
		keys = append(keys, k)
	}
	for k := range subtitlesExtractors {
		keys = append(keys, k)
	}
	return keys
}

// Extract 单个帖子提取
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	fn, ok := extractors[req.Extractor]
	if !ok {
		return nil, fmt.Errorf("extractor not found: %s", req.Extractor)
	}
	return fn(ctx, req)
}

// ExtractPlaylist 播放列表提取
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	fn, ok := playlistExtractors[req.Extractor]
	if !ok {
		return nil, fmt.Errorf("extractor not found: %s", req.Extractor)
	}
	return fn(ctx, req)
}

// ExtractSubtitles 字幕提取
func ExtractSubtitles(ctx context.Context, req *schema.ExtractReq) (*schema.SubtitlesResp, error) {
	fn, ok := subtitlesExtractors[req.Extractor]
	if !ok {
		return nil, fmt.Errorf("extractor not found: %s", req.Extractor)
	}
	return fn(ctx, req)
}
