package suno_top

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"net/url"
)

const (
	validateLinkAPI = "https://suno-top.com/api/suno/validateLink"
	downloadLinkAPI = "https://suno-top.com/api/suno/getSongInfo?link=%s&status=2"
)

type result struct {
	Data int `json:"data"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	paths := urlx.ParsePath(req.URL)
	if len(paths) < 2 {
		return nil, extract.ErrUnsupportedURL
	}

	if paths[0] == "song" {
		audioURL := fmt.Sprintf("https://cdn1.suno.ai/%s.mp3", paths[1])
		videoURL := fmt.Sprintf("https://cdn1.suno.ai/%s.mp4", paths[1])
		return &schema.Post{
			Medias: []*schema.Media{
				schema.NewAudioMedia(audioURL, ""),
				schema.NewVideoMedia(videoURL, ""),
			},
		}, nil
	}

	if paths[0] != "s" {
		return nil, extract.ErrUnsupportedURL
	}

	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParam("link", req.URL).
		SetResult(&result).
		Get(validateLinkAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("validateLinkAPI, http status: %d, body: %s", resp.StatusCode(), resp.String())
	}
	if result.Data != 2 {
		return nil, fmt.Errorf("validateLinkAPI, data: %d", result.Data)
	}
	return &schema.Post{
		Medias: []*schema.Media{
			schema.NewAudioMedia(fmt.Sprintf(downloadLinkAPI, url.QueryEscape(req.URL)), ""),
		},
	}, nil
}
