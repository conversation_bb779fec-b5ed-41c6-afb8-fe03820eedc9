package suno_top

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://app.suno.ai/song/5b0ec0ca-6063-4a21-95b9-1433c35a3ded
// https://app.suno.ai/song/9cd7f8cc-d9e5-4d10-b1a0-3b5fad2418c5
// https://app.suno.ai/song/ee467d00-5813-4a74-9792-c9ae4a09d344
// https://app.suno.ai/song/5da8af6f-30be-4559-a1e2-275b874a14e2
// https://suno.com/song/7ee5b5c0-046a-43f4-8ecd-f022146ff11b?sh=zAJLs9XMH03ytXLl
// https://suno.com/song/c8b59530-941b-48ae-965e-88671f095b85
// https://suno.com/song/18841f76-7a14-42c1-93cf-13631504c522
// https://suno.com/s/zAJLs9XMH03ytXLl
// https://suno.com/s/zAJLs9XMH03ytXLl
// https://suno.com/s/zAJLs9XMH03ytXLl
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://app.suno.ai/song/5da8af6f-30be-4559-a1e2-275b874a14e2",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
