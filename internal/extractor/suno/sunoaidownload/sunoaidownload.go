package sunoaidownload

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
)

var headers = map[string]string{
	"accept":             "*/*",
	"accept-language":    "en,ja;q=0.9,zh;q=0.8,zh-TW;q=0.7,es-CO;q=0.6,es;q=0.5,zh-HK;q=0.4,zh-CN;q=0.3,en-US;q=0.2",
	"content-type":       "application/json",
	"origin":             "https://sunoaidownload.com",
	"referer":            "https://sunoaidownload.com/",
	"sec-ch-ua":          `"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"`,
	"sec-ch-ua-mobile":   "?0",
	"sec-ch-ua-platform": `"macOS"`,
	"sec-fetch-dest":     "empty",
	"sec-fetch-mode":     "cors",
	"sec-fetch-site":     "same-origin",
	"user-agent":         "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
}

type result struct {
	Title string `json:"title"`
	Audio string `json:"audio"`
	Image string `json:"image"`
	Video string `json:"video"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	var result result
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetBody(map[string]string{"url": req.URL}).
		SetResult(&result).
		Post("https://sunoaidownload.com/api/sniff")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to fetch data, http status: %d, body: %s", resp.StatusCode(), resp.String())
	}
	post := &schema.Post{
		Text: result.Title,
		Medias: []*schema.Media{
			schema.NewAudioMedia(result.Audio, result.Image),
		},
	}
	if result.Video != "" {
		post.Medias = append(post.Medias, schema.NewVideoMedia(result.Video, ""))
	}
	return post, nil
}
