package twitter

import (
	"context"
	"errors"
	"fmt"
	"garden/internal/constant"
	"garden/internal/service"
	"github.com/gongyinshi/shared/urlx"
	"strings"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
)

var (
	Twitter135Headers = map[string]string{
		"X-RapidAPI-Key":  "**************************************************",
		"X-RapidAPI-Host": "twitter135.p.rapidapi.com",
	}
	Twitter32Headers = map[string]string{
		"X-RapidAPI-Key":  "**************************************************",
		"X-RapidAPI-Host": "twitter32.p.rapidapi.com",
	}
	Twitter241Headers = map[string]string{
		"X-RapidAPI-Key":  "**************************************************",
		"X-RapidAPI-Host": "twitter241.p.rapidapi.com",
	}
	TwttrapiHeaders = map[string]string{
		"X-RapidAPI-Key":  "**************************************************",
		"X-RapidAPI-Host": "twttrapi.p.rapidapi.com",
	}
	GotoService *service.GotoService
)

func init() {
	aes, err := service.NewAES()
	if err != nil {
		panic(err)
	}
	GotoService = service.NewGotoService(aes, nil, nil)
}

// TwitterUserInfo Twitter用户信息结构体
type TwitterUserInfo struct {
	UID        string
	Username   string
	Fullname   string
	ProfilePic string
	Verified   bool
}

type TwitterScreenshotPayload struct {
	Text            string `json:"text"`
	CreatedAt       string `json:"created_at"`
	Username        string `json:"username"`
	Fullname        string `json:"fullname"`
	Verified        bool   `json:"verified"`
	ProfileImageURL string `json:"profile_image_url"`
}

func VideoVariantCompare(a, b gjson.Result) int {
	// 1. 比较 content_type（video/mp4 优先）
	aContentType := a.Get("content_type").String()
	bContentType := b.Get("content_type").String()
	if aContentType != bContentType {
		if aContentType == "video/mp4" {
			return -1 // a 排在 b 前面
		} else if bContentType == "video/mp4" {
			return 1 // b 排在 a 前面
		}
	}

	// 2. 比较 bitrate（降序）
	aBitrate := a.Get("bitrate").Int()
	if !a.Get("bitrate").Exists() {
		aBitrate = a.Get("bit_rate").Int() // 回退到 bit_rate
	}
	bBitrate := b.Get("bitrate").Int()
	if !b.Get("bitrate").Exists() {
		bBitrate = b.Get("bit_rate").Int() // 回退到 bit_rate
	}
	return int(bBitrate - aBitrate) // 降序
}

// ExtractTweetID 从Twitter URL中提取推文ID
func ExtractTweetID(url string) (string, error) {
	pathList := urlx.ParsePath(url)
	if len(pathList) == 0 {
		return "", fmt.Errorf("not tweet url: no path")
	}

	// 查找"status"在路径中的位置
	for i, part := range pathList {
		if part == "status" && i+1 < len(pathList) {
			return pathList[i+1], nil
		}
	}
	return "", fmt.Errorf("not tweet url: no status in path")
}

// GetTwitterProfilePic200x200 获取200x200尺寸的Twitter头像
func GetTwitterProfilePic200x200(url string) string {
	if url == "" {
		return ""
	}
	return strings.Replace(url, "_normal", "_200x200", 1)
}

// GetRandomTwttrapiHeaders 随机选择API Key并返回包含RapidAPI相关headers
func GetRandomTwttrapiHeaders() map[string]string {
	keys := []string{
		"**************************************************",
		"690ae987f0mshf97f2dec4174023p1be898jsn89f4e912f1fd",
	}
	return map[string]string{
		"X-RapidAPI-Key":  lo.Sample(keys),
		"X-RapidAPI-Host": "twttrapi.p.rapidapi.com",
	}
}

// GetUserinfoByURL 从Twitter URL获取用户信息
func GetUserinfoByURL(url string) (TwitterUserInfo, error) {
	pathList := urlx.ParsePath(url)

	if len(pathList) == 0 {
		return TwitterUserInfo{}, errors.New("invalid user page url")
	}

	invalidPaths := []string{"home", "explore", "notifications", "messages", "search", "i"}
	for _, p := range invalidPaths {
		if pathList[0] == p {
			return TwitterUserInfo{}, errors.New("invalid user page url")
		}
	}

	return GetUserinfoByUsername(pathList[0])
}

// GetUserinfoByURL 从Twitter URL获取用户信息
func GetUserinfoByUsername(username string) (TwitterUserInfo, error) {

	var result postResult
	_, err := constant.RestyClient.R().SetContext(context.Background()).
		SetHeaders(Twitter241Headers).
		SetQueryParam("username", username).
		SetResult(&result).
		Get("https://twitter241.p.rapidapi.com/user")
	if err != nil {
		return TwitterUserInfo{}, err
	}

	if result.Result.Data.User.Result.Typename == "UserUnavailable" {
		return TwitterUserInfo{}, errors.New("user not exist")
	}

	userInfo := result.Result.Data.User.Result.Legacy
	return TwitterUserInfo{
		UID:        result.Result.Data.User.Result.RestID,
		Username:   userInfo.ScreenName,
		Fullname:   userInfo.Name,
		ProfilePic: GetTwitterProfilePic200x200(userInfo.ProfileImageURLHTTPS),
		Verified:   userInfo.Verified,
	}, nil
}
