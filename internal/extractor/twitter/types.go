package twitter

type postResult struct {
	Result struct {
		Data struct {
			User struct {
				Result struct {
					RestID   string `json:"rest_id"`
					Typename string `json:"__typename"`
					Legacy   struct {
						ScreenName           string `json:"screen_name"`
						Name                 string `json:"name"`
						ProfileImageURLHTTPS string `json:"profile_image_url_https"`
						Verified             bool   `json:"verified"`
					} `json:"legacy"`
				} `json:"result"`
			} `json:"user"`
		} `json:"data"`
	} `json:"result"`
}

type Variant struct {
	ContentType string `json:"content_type"`
	URL         string `json:"url"`
	Bitrate     int    `json:"bitrate,omitempty"`
}
