package twitter241

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/twitter"
	"garden/internal/schema"
	"sort"
	"time"

	"github.com/tidwall/gjson"
)

const (
	postAPI     = "https://twitter241.p.rapidapi.com/tweet"
	playlistAPI = "https://twitter241.p.rapidapi.com/user-media"
)

var headers = map[string]string{
	"x-rapidapi-host": "twitter241.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

// https://rapidapi.com/davethebeast/api/twitter241/playground/apiendpoint_eab00723-ecc9-4fd0-994e-698d471075e5
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	tweetID, err := twitter.ExtractTweetID(req.URL)
	if err != nil {
		return nil, err
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("pid", tweetID).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] twitter241: %s", resp.String())
	}
	result := resp.String()
	var tweetLegacy, author gjson.Result
	if gjson.Get(result, "tweet").Exists() {
		tweetLegacy = gjson.Get(result, "tweet")
		author = gjson.Get(result, "user.legacy")
	} else {
		if !gjson.Get(result, "data.threaded_conversation_with_injections_v2").Exists() {
			return nil, fmt.Errorf("[playlist] twitter241: %s", result)
		}
		instructions := gjson.Get(result, "data.threaded_conversation_with_injections_v2.instructions").Array()
		entries := []gjson.Result{}
		for _, instruction := range instructions {
			entries = instruction.Get("entries").Array()
			if len(entries) != 0 {
				break
			}
		}
		var tweet gjson.Result
		for _, entry := range entries {
			if entry.Get("entryId").String() == fmt.Sprintf("tweet-%s", tweetID) {
				tweet = entry // 找到匹配项
			} else {
				tweet = entries[0]
			}
		}
		res := tweet.Get("content.itemContent.tweet_results.result")
		// 1. 先尝试直接获取 "legacy"
		legacy := res.Get("legacy")
		if legacy.Exists() {
			tweetLegacy = legacy
		} else {
			tweetLegacy = res.Get("tweet.legacy")
		}
		author = res.Get("result.core.user_results.result.legacy")
	}
	post := &schema.Post{
		Text: tweetLegacy.Get("full_text").String(),
	}
	medias := tweetLegacy.Get("extended_entities.media").Array()
	for _, media := range medias {
		if media.Get("type").String() == "photo" {
			post.Medias = append(post.Medias, schema.NewImageMedia(media.Get("media_url_https").String()))
		} else {
			variants := media.Get("video_info.variants").Array()
			sort.Slice(variants, func(i, j int) bool {
				return twitter.VideoVariantCompare(variants[i], variants[j]) < 0
			})
			post.Medias = append(post.Medias, schema.NewVideoMedia(variants[0].Get("url").String(), media.Get("media_url_https").String()))
		}
	}
	// 解析时间
	createdAtTime, err := time.Parse(time.RubyDate, tweetLegacy.Get("created_at").String())
	if err != nil {
		return nil, fmt.Errorf("Error parsing time: %v", err)
	}
	// 转换为本地时区（如果需要）
	createdAtTime = createdAtTime.In(time.Local)

	// 格式化时间
	formattedTime := createdAtTime.Format("15:04 · 2006/01/02")
	payload := schema.Payload{
		"text":              post.Text,
		"created_at":        formattedTime,
		"username":          author.Get("screen_name").String(),
		"fullname":          author.Get("name").String(),
		"verified":          author.Get("verified").Bool(),
		"profile_image_url": twitter.GetTwitterProfilePic200x200(author.Get("profile_image_url_https").String()),
	}
	screenshotURL, err := twitter.GotoService.GenerateStreamURL(payload, true)
	if err != nil {
		return nil, err
	}
	post.Medias = append(post.Medias, schema.NewImageMedia(screenshotURL))

	return post, nil
}
