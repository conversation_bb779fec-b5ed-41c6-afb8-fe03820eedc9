package twitter241

import (
	"context"
	"garden/internal/schema"
	"garden/pkg/twitter_shot"
	"testing"
	"time"
)

// https://twitter.com/whyyoutouzhele/status/1651379323968925699
// https://twitter.com/NoContextHumans/status/1651180154188713986
// https://x.com/heartsonyoga/status/1920726234600010242
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://twitter.com/NoContextHumans/status/1651180154188713986",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post.Text)
	payload := twitter_shot.Payload{
		Fullname:        "测试用户",
		Username:        "testuser",
		Verified:        false,
		Text:            post.Text,
		CreatedAt:       time.Now().Format("Jan 02,2006"),
		ProfileImageURL: "https://pbs.twimg.com/profile_images/1558667234855292929/RqgodvGb_200x200.jpg", // 可忽略，使用本地 static/twitter_logo.png
	}
	twitter_shot.NewClient().GenerateTwitterShot(payload)
}

// https://twitter.com/Nike
// https://twitter.com/adidas
func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://twitter.com/Nike",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(playlist.Posts)
}
