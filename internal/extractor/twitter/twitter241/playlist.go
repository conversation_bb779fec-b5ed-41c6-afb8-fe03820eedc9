package twitter241

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/twitter"
	"garden/internal/schema"
	"sort"
	"time"

	"github.com/tidwall/gjson"
)

// https://rapidapi.com/davethebeast/api/twitter241/playground/apiendpoint_310ad185-b110-4c1a-93c2-fac77ebb6713
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	author, err := twitter.GetUserinfoByURL(req.URL)
	if err != nil {
		return nil, err
	}

	params := map[string]string{
		"user":  author.UID,
		"count": "100",
	}
	if req.Cursor != "" {
		params["cursor"] = req.Cursor
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(twitter.Twitter241Headers).
		SetQueryParams(params).
		Get(playlistAPI)
	if err != nil {
		return nil, err
	}
	if resp.Is<PERSON>rror() {
		return nil, fmt.Errorf("[playlist] twitter241: %s", resp.String())
	}
	playlist := &schema.Playlist{}
	result := resp.String()
	if gjson.Get(result, "error").Exists() {
		return nil, fmt.Errorf("[playlist] twitter241: %s", gjson.Get(result, "error.message").String())
	}
	if !gjson.Get(result, "result.timeline").Exists() {
		return nil, fmt.Errorf("处理失败，请重试")
	}
	var instruction gjson.Result
	for _, inst := range gjson.Get(result, "result.timeline.instructions").Array() {
		if inst.Get("entries").Exists() {
			instruction = inst
		} else {
			continue
		}
	}
	for _, entry := range instruction.Get("entries").Array() {
		info := entry.Get("content.itemContent.tweet_results.result.core.user_results.result")

		var legacy gjson.Result
		if info.Get("legacy").Exists() {
			legacy = info.Get("legacy")
		} else {
			legacy = info.Get("tweet.legacy")
		}
		medias := legacy.Get("extended_entities.media").Array()
		if legacy.Get("created_at").String() == "" {
			continue
		}
		createdAtDatetime, err := time.Parse(time.RubyDate, legacy.Get("created_at").String())
		if err != nil {
			return nil, err
		}

		tweetID := legacy.Get("id_str").String()
		post := &schema.Post{
			ID:         tweetID,
			Text:       legacy.Get("full_text").String(),
			CreateTime: int(createdAtDatetime.Unix()),
		}
		if len(medias) > 0 {
			for _, media := range medias {
				previewURL := media.Get("media_url_https").String()
				if media.Get("type").String() == "photo" {
					post.Medias = append(post.Medias, schema.NewImageMedia(previewURL))
				} else {
					variants := media.Get("video_info.variants").Array()
					sort.Slice(variants, func(i, j int) bool {
						return twitter.VideoVariantCompare(variants[i], variants[j]) < 0
					})
					videoMedia := schema.NewVideoMedia(variants[0].Get("url").String(), media.Get("media_url_https").String())
					post.Medias = append(post.Medias, videoMedia)
				}
			}
		} else {
			payload := schema.Payload{
				"text":              post.Text,
				"created_at":        createdAtDatetime.Format("15:04 · 2006/01/02"),
				"username":          author.Username,
				"fullname":          author.Fullname,
				"verified":          author.Verified,
				"profile_image_url": author.ProfilePic,
			}
			screenshotURL, err := twitter.GotoService.GenerateStreamURL(payload, true)
			if err != nil {
				return nil, err
			}
			post.Medias = append(post.Medias, schema.NewImageMedia(screenshotURL))
		}
		playlist.Posts = append(playlist.Posts, post)
	}

	// 设置用户信息
	playlist.User = &schema.User{
		Username: author.Fullname,
		Avatar:   author.ProfilePic,
	}

	// 设置分页信息
	if len(playlist.Posts) > 0 {
		playlist.HasMore = true
		playlist.NextCursor = gjson.Get(result, "cursor.bottom").String()
	}

	return playlist, nil
}
