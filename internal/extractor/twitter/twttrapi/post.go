package twttrapi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/twitter"
	"garden/internal/schema"
	"sort"
	"time"

	"github.com/tidwall/gjson"
)

const (
	postAPI     = "https://twttrapi.p.rapidapi.com/get-tweet"
	playlistAPI = "https://twttrapi.p.rapidapi.com/user-media"
)

// https://rapidapi.com/twttrapi-twttrapi-default/api/twttrapi/playground/apiendpoint_f9bad9a2-c29e-4360-83f5-62bd775838b7
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	post := &schema.Post{}
	tweetID, err := twitter.ExtractTweetID(req.URL)
	if err != nil {
		return nil, err
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(twitter.GetRandomTwttrapiHeaders()).
		SetQueryParam("tweet_id", tweetID).
		Get(postAPI)

	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] twttrapi: %s", resp.String())
	}

	result := gjson.Parse(resp.String())
	if result.Get("error").Exists() || !result.Get("data.tweet_result").Exists() {
		return nil, fmt.Errorf("[playlist] twttrapi: %s", resp.String())
	}

	res := result.Get("data.tweet_result.result")
	var tweetLegacy gjson.Result
	if res.Get("legacy").Exists() {
		tweetLegacy = res.Get("legacy")
	} else {
		tweetLegacy = res.Get("tweet.legacy")
	}
	author := res.Get("core.user_result.result.legacy")
	post.Text = tweetLegacy.Get("full_text").String()
	for _, media := range tweetLegacy.Get("extended_entities.media").Array() {
		if media.Get("type").String() == "photo" {
			post.Medias = append(post.Medias, schema.NewImageMedia(media.Get("media_url_https").String()))
		} else {
			variants := media.Get("video_info.variants").Array()
			sort.Slice(variants, func(i, j int) bool {
				return twitter.VideoVariantCompare(variants[i], variants[j]) < 0
			})
			post.Medias = append(post.Medias, schema.NewVideoMedia(variants[0].Get("url").String(), media.Get("media_url_https").String()))
		}
	}

	// 解析时间
	createdAtTime, err := time.Parse(time.RubyDate, tweetLegacy.Get("created_at").String())
	if err != nil {
		return nil, fmt.Errorf("Error parsing time: %v", err)
	}
	// 转换为本地时区（如果需要）
	createdAtTime = createdAtTime.In(time.Local)

	// 格式化时间
	payload := schema.Payload{
		"text":              post.Text,
		"created_at":        createdAtTime.Format("15:04 · 2006/01/02"),
		"username":          author.Get("screen_name").String(),
		"fullname":          author.Get("name").String(),
		"verified":          author.Get("verified").Bool(),
		"profile_image_url": author.Get("profile_image_url_https").String(),
	}
	screenshotURL, err := twitter.GotoService.GenerateStreamURL(payload, true)
	if err != nil {
		return nil, err
	}
	post.Medias = append(post.Medias, schema.NewImageMedia(screenshotURL))

	return post, nil
}
