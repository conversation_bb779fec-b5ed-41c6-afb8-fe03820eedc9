package twttrapi

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://twitter.com/whyyoutouzhele/status/1651379323968925699
// https://twitter.com/NoContextHumans/status/1651180154188713986
// https://x.com/heartsonyoga/status/1920726234600010242
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://x.com/435hz/status/1922226536456810593",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post.Text)
}

// https://twitter.com/Nike
// https://twitter.com/adidas
func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://twitter.com/Nike",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(playlist)
}
