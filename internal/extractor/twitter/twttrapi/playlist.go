package twttrapi

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/twitter"
	"garden/internal/schema"
	"sort"
	"time"

	"github.com/tidwall/gjson"
)

// https://rapidapi.com/twttrapi-twttrapi-default/api/twttrapi/playground/apiendpoint_1536078c-d353-43a8-970e-89e00e56f84a
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (*schema.Playlist, error) {
	playlist := &schema.Playlist{}
	author, err := twitter.GetUserinfoByURL(req.URL)
	if err != nil {
		return nil, err
	}
	params := map[string]string{
		"user_name": author.Username,
		"user_id":   author.UID,
	}
	if req.Cursor != "" {
		params["cursor"] = req.Cursor
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(twitter.GetRandomTwttrapiHeaders()).
		SetQueryParams(params).
		Get(playlistAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] twttrapi: %s", resp.String())
	}
	result := gjson.Parse(resp.String())
	var instruction gjson.Result
	for _, inst := range result.Get("data.user_result.result.timeline_response.timeline.instructions").Array() {
		if inst.Get("entries").Exists() {
			instruction = inst
		} else {
			continue
		}
	}
	for _, entry := range instruction.Get("entries").Array() {
		info := entry.Get("content.content.tweetResult.result")

		var legacy gjson.Result
		if info.Get("legacy").Exists() {
			legacy = info.Get("legacy")
		} else {
			legacy = info.Get("tweet.legacy")
		}
		medias := legacy.Get("extended_entities.media").Array()
		if legacy.Get("created_at").String() == "" {
			continue
		}
		createdAtDatetime, err := time.Parse(time.RubyDate, legacy.Get("created_at").String())
		if err != nil {
			return nil, err
		}

		tweetID := legacy.Get("conversation_id_str").String()
		post := &schema.Post{
			ID:         tweetID,
			Text:       legacy.Get("full_text").String(),
			CreateTime: int(createdAtDatetime.Unix()),
		}
		if len(medias) > 0 {
			for _, media := range medias {
				previewURL := media.Get("media_url_https").String()
				if media.Get("type").String() == "photo" {
					post.Medias = append(post.Medias, schema.NewImageMedia(previewURL))
				} else {
					variants := media.Get("video_info.variants").Array()
					sort.Slice(variants, func(i, j int) bool {
						return twitter.VideoVariantCompare(variants[i], variants[j]) < 0
					})
					videoMedia := schema.NewVideoMedia(variants[0].Get("url").String(), media.Get("media_url_https").String())
					post.Medias = append(post.Medias, videoMedia)
				}
			}
		} else {
			payload := schema.Payload{
				"text":              post.Text,
				"created_at":        createdAtDatetime.Format("15:04 · 2006/01/02"),
				"username":          author.Username,
				"fullname":          author.Fullname,
				"verified":          author.Verified,
				"profile_image_url": author.ProfilePic,
			}
			screenshotURL, err := twitter.GotoService.GenerateStreamURL(payload, true)
			if err != nil {
				return nil, err
			}
			post.Medias = append(post.Medias, schema.NewImageMedia(screenshotURL))
		}
		playlist.Posts = append(playlist.Posts, post)
	}

	playlist.User = &schema.User{
		Username: author.Fullname,
		Avatar:   author.ProfilePic,
	}

	return playlist, nil
}
