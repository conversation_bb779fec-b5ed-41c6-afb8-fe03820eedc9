// Code generated by extractor/gen. DO NOT EDIT.
// Code generated by extractor/gen. DO NOT EDIT.
// Code generated by extractor/gen. DO NOT EDIT.

package extractor

import (
	"garden/internal/extractor/bilibili"
	"garden/internal/extractor/facebook/facebook_reel_and_video_downloader"
	"garden/internal/extractor/facebook/fdown"
	"garden/internal/extractor/instagram/flashapi1"
	"garden/internal/extractor/instagram/instagram28"
	"garden/internal/extractor/instagram/instagram_bulk_profile_scrapper"
	"garden/internal/extractor/instagram/instagram_downloader"
	"garden/internal/extractor/instagram/instagram_looter2"
	"garden/internal/extractor/kg"
	"garden/internal/extractor/meipai"
	"garden/internal/extractor/momo"
	"garden/internal/extractor/partner/ake999"
	"garden/internal/extractor/partner/ake999_xhs"
	"garden/internal/extractor/partner/diadi"
	"garden/internal/extractor/partner/doudou"
	"garden/internal/extractor/partner/guijianpan"
	"garden/internal/extractor/partner/hlytools"
	"garden/internal/extractor/partner/ip13"
	"garden/internal/extractor/partner/ip13_backup"
	"garden/internal/extractor/partner/lux"
	"garden/internal/extractor/partner/vnil"
	"garden/internal/extractor/partner/yijianjiexi"
	"garden/internal/extractor/partner/you_get"
	"garden/internal/extractor/partner/yt_dlp"
	"garden/internal/extractor/pinterest/fastest_social_video_and_image_downloader"
	"garden/internal/extractor/suno/suno_top"
	"garden/internal/extractor/suno/sunoaidownload"
	"garden/internal/extractor/threads/sssthreads"
	"garden/internal/extractor/threads/threads_by_meta"
	"garden/internal/extractor/tiktok/tiktok_best_experience"
	"garden/internal/extractor/tiktok/tiktok_video_no_watermark"
	"garden/internal/extractor/triller"
	"garden/internal/extractor/twitter/twitter241"
	"garden/internal/extractor/twitter/twttrapi"
	"garden/internal/extractor/weibo"
	"garden/internal/extractor/weishi"
	"garden/internal/extractor/xiaohongshu"
	"garden/internal/extractor/xiaoying"
	"garden/internal/extractor/youtube/youtube_media_downloader"
	"garden/internal/extractor/youtube/yt_api"
	"garden/internal/extractor/yunyinyue"
)

func init() {
	// 单个帖子提取
	// bilibili
	extractors["bilibili"] = bilibili.Extract
	// facebook
	extractors["facebook/facebook_reel_and_video_downloader"] = facebook_reel_and_video_downloader.Extract
	extractors["facebook/fdown"] = fdown.Extract
	// instagram
	extractors["instagram/flashapi1"] = flashapi1.Extract
	extractors["instagram/instagram28"] = instagram28.Extract
	extractors["instagram/instagram_bulk_profile_scrapper"] = instagram_bulk_profile_scrapper.Extract
	extractors["instagram/instagram_downloader"] = instagram_downloader.Extract
	extractors["instagram/instagram_looter2"] = instagram_looter2.Extract
	// kg
	extractors["kg"] = kg.Extract
	// meipai
	extractors["meipai"] = meipai.Extract
	// momo
	extractors["momo"] = momo.Extract
	// partner
	extractors["partner/ake999"] = ake999.Extract
	extractors["partner/ake999_xhs"] = ake999_xhs.Extract
	extractors["partner/diadi"] = diadi.Extract
	extractors["partner/doudou"] = doudou.Extract
	extractors["partner/guijianpan"] = guijianpan.Extract
	extractors["partner/ip13"] = ip13.Extract
	extractors["partner/ip13_backup"] = ip13_backup.Extract
	extractors["partner/lux"] = lux.Extract
	extractors["partner/vnil"] = vnil.Extract
	extractors["partner/yijianjiexi"] = yijianjiexi.Extract
	extractors["partner/you_get"] = you_get.Extract
	extractors["partner/yt_dlp"] = yt_dlp.Extract
	// pinterest
	extractors["pinterest/fastest_social_video_and_image_downloader"] = fastest_social_video_and_image_downloader.Extract
	// suno
	extractors["suno/suno_top"] = suno_top.Extract
	extractors["suno/sunoaidownload"] = sunoaidownload.Extract
	// threads
	extractors["threads/sssthreads"] = sssthreads.Extract
	extractors["threads/threads_by_meta"] = threads_by_meta.Extract
	// tiktok
	extractors["tiktok/tiktok_best_experience"] = tiktok_best_experience.Extract
	extractors["tiktok/tiktok_video_no_watermark"] = tiktok_video_no_watermark.Extract
	// triller
	extractors["triller"] = triller.Extract
	// twitter
	extractors["twitter/twitter241"] = twitter241.Extract
	extractors["twitter/twttrapi"] = twttrapi.Extract
	// weibo
	extractors["weibo"] = weibo.Extract
	// weishi
	extractors["weishi"] = weishi.Extract
	// xiaohongshu
	extractors["xiaohongshu"] = xiaohongshu.Extract
	// xiaoying
	extractors["xiaoying"] = xiaoying.Extract
	// youtube
	extractors["youtube/youtube_media_downloader"] = youtube_media_downloader.Extract
	extractors["youtube/yt_api"] = yt_api.Extract
	// yunyinyue
	extractors["yunyinyue"] = yunyinyue.Extract

	// 播放列表/频道/主页批量提取
	// bilibili
	playlistExtractors["playlist/bilibili"] = bilibili.ExtractPlaylist
	// instagram
	playlistExtractors["playlist/instagram/flashapi1"] = flashapi1.ExtractPlaylist
	playlistExtractors["playlist/instagram/instagram28"] = instagram28.ExtractPlaylist
	playlistExtractors["playlist/instagram/instagram_bulk_profile_scrapper"] = instagram_bulk_profile_scrapper.ExtractPlaylist
	playlistExtractors["playlist/instagram/instagram_looter2"] = instagram_looter2.ExtractPlaylist
	// partner
	playlistExtractors["playlist/partner/diadi"] = diadi.ExtractPlaylist
	playlistExtractors["playlist/partner/hlytools"] = hlytools.ExtractPlaylist
	playlistExtractors["playlist/partner/vnil"] = vnil.ExtractPlaylist
	playlistExtractors["playlist/partner/yijianjiexi"] = yijianjiexi.ExtractPlaylist
	// tiktok
	playlistExtractors["playlist/tiktok/tiktok_best_experience"] = tiktok_best_experience.ExtractPlaylist
	// twitter
	playlistExtractors["playlist/twitter/twitter241"] = twitter241.ExtractPlaylist
	playlistExtractors["playlist/twitter/twttrapi"] = twttrapi.ExtractPlaylist
	// weibo
	playlistExtractors["playlist/weibo"] = weibo.ExtractPlaylist
	// youtube
	playlistExtractors["playlist/youtube/youtube_media_downloader"] = youtube_media_downloader.ExtractPlaylist

	// 字幕提取
	// youtube
	subtitlesExtractors["subtitles/youtube/youtube_media_downloader"] = youtube_media_downloader.ExtractSubtitles
	subtitlesExtractors["subtitles/youtube/yt_api"] = yt_api.ExtractSubtitles
}
