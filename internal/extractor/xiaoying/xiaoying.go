package xiaoying

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"github.com/gongyinshi/shared/urlx"
	"slices"
	"strings"

	"github.com/samber/lo"
)

const (
	coverAPI = "http://w.api.xiaoying.co/webapi2/rest/video/publishinfo.get?appkey=30000000&ver=1&format=MP4"
	videoAPI = "http://w.api.xiaoying.co/webapi2/rest/video/videourl?appkey=30000000&ver=1&format=MP4"
)

type videoResult struct {
	URL string `json:"url"`
}

type coverResult struct {
	VideoInfo struct {
		Title    string `json:"title"`
		Desc     string `json:"desc"`
		CoverURL string `json:"coverurl"`
	} `json:"videoinfo"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	paths := urlx.ParsePath(req.URL)
	idx := slices.Index(paths, "v")
	if idx == -1 {
		return nil, extract.ErrUnsupportedURL
	}
	videoID := lo.NthOrEmpty(paths, idx+1)
	if videoID == "" {
		return nil, extract.ErrUnsupportedURL
	}

	var videoResult videoResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader("User-Agent", useragent.RandomMobile()).
		SetQueryParam("puid", videoID).
		SetResult(&videoResult).
		Get(videoAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to get video url: %s", resp.String())
	}
	if videoResult.URL == "" {
		return nil, fmt.Errorf("接口结果里没有视频地址")
	}

	var coverResult coverResult
	resp, err = constant.RestyClient.R().SetContext(ctx).
		SetHeader("User-Agent", useragent.RandomMobile()).
		SetQueryParam("puid", videoID).
		SetResult(&coverResult).
		Get(coverAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to get cover url: %s", resp.String())
	}
	coverURL := coverResult.VideoInfo.CoverURL
	if idx := strings.Index(coverURL, "!"); idx != -1 {
		coverURL = coverURL[:idx]
	}

	return &schema.Post{
		Text: lo.CoalesceOrEmpty(coverResult.VideoInfo.Desc, coverResult.VideoInfo.Title),
		Medias: []*schema.Media{
			schema.NewVideoMedia(videoResult.URL, coverURL),
		},
	}, nil
}
