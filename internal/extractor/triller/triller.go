package triller

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/internal/service/redirect"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"github.com/gongyinshi/shared/urlx"
	"regexp"
	"slices"

	"github.com/samber/lo"
)

var (
	// https://v.triller.co/60y57O
	// https://v.triller.co/EGGAkm
	shortURLRegex = regexp.MustCompile(`https:\/\/v\.triller\.co\/[A-Za-z0-9]+`)

	headers = map[string]string{
		"authority":        "social.triller.co",
		"sec-ch-ua":        `" Not;A Brand";v="99", "Google Chrome";v="91", "Chromium";v="91"`,
		"sec-ch-ua-mobile": "?1",
		"user-agent":       "Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Mobile Safari/537.36",
		"accept":           "*/*",
		"origin":           "https://triller.co",
		"sec-fetch-site":   "same-site",
		"sec-fetch-mode":   "cors",
		"sec-fetch-dest":   "empty",
		"accept-language":  "en-US,en;q=0.9",
	}
)

const (
	api = "https://social.triller.co/v1.5/api/videos/%s"
)

type result struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Videos  []struct {
		VideoURL     string `json:"video_url"`
		ThumbnailURL string `json:"thumbnail_url"`
		Description  string `json:"description"`
	} `json:"videos"`
}

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	req.URL = unshorten(ctx, req.URL)
	paths := urlx.ParsePath(req.URL)
	idx := slices.Index(paths, "video")
	if idx == -1 {
		return nil, extract.ErrUnsupportedURL
	}
	videoID := lo.NthOrEmpty(paths, idx+1)
	if videoID == "" {
		return nil, extract.ErrUnsupportedURL
	}
	var result result
	_, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetResult(&result).
		SetError(&result).
		Get(fmt.Sprintf(api, videoID))
	if err != nil {
		return nil, err
	}
	if !result.Status {
		if result.Message == "Video is private" {
			return nil, extract.ErrNonPublicContent
		}
		return nil, fmt.Errorf("triller: %v", result.Message)
	}
	if len(result.Videos) == 0 {
		return nil, fmt.Errorf("triller: no videos found")
	}

	data := result.Videos[0]

	return &schema.Post{
		Text: data.Description,
		Medias: []*schema.Media{
			schema.NewVideoMedia(data.VideoURL, data.ThumbnailURL),
		},
	}, nil
}

// unshorten 将短链接转换为长链接
func unshorten(ctx context.Context, originalURL string) string {
	if !shortURLRegex.MatchString(originalURL) {
		return originalURL
	}
	return redirect.ResolveFinalURL(ctx, originalURL, &redirect.Options{
		UserAgent: useragent.Desktop(),
	})
}
