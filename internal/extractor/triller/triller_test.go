package triller

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://triller.co/@loren/video/64858682
// https://triller.co/@loren/video/64858682
// https://triller.co/@ninadrama/video/61353819
// https://v.triller.co/60y57O
// 非公开内容: https://v.triller.co/EGGAkm
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://triller.co/@loren/video/648586829",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
