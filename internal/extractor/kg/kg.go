package kg

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/useragent"
	"github.com/tidwall/gjson"
	"regexp"
	"strings"
)

var re = regexp.MustCompile(`>window\.__DATA__=(.+);</script>`)

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderUserAgent, useragent.RandomDesktop()).
		Get(req.URL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("kg: %s", resp.String())
	}
	body := strings.ReplaceAll(resp.String(), " ", "")
	matches := re.FindStringSubmatch(body)
	if len(matches) < 2 {
		return nil, fmt.Errorf("页面html中未匹配到视频信息")
	}

	// Parse JSON
	jsonStr := matches[1]

	// 使用 gjson 解析
	detail := gjson.Get(jsonStr, "detail")
	videoURL := detail.Get("playurl_video").String()
	audioURL := detail.Get("playurl").String()
	content := detail.Get("content").String()
	cover := detail.Get("cover").String()
	post := &schema.Post{
		Text: content,
	}

	if videoURL == "" && audioURL == "" {
		return nil, fmt.Errorf("视频和音频都不包含,url = %s", req.URL)
	}
	if videoURL != "" {
		post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, cover))
	}
	if audioURL != "" {
		post.Medias = append(post.Medias, schema.NewAudioMedia(audioURL, cover))
	}

	return post, nil
}
