package weibo

import (
	"context"
	"encoding/json"
	"garden/internal/schema"
	"testing"
)

// 绿洲 https://m.oasis.weibo.cn/v1/h5/share?sid=4646434270085670
// 视频 https://weibo.com/2142058927/Eg0OBB5A5
// 视频 https://m.weibo.cn/status/4114395665972373
// 多个视频 https://weibo.com/7790166853/4937617801481842
// 视频 https://m.weibo.cn/status/4736844845030203
// 图片 https://m.weibo.cn/status/4844759925393197
// https://weibo.com/tv/show/1034:5167672615436329?mid=5167675010452529
// https://weibo.com/tv/show/1034:5167983803170850?mid=5167986331881574
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://weibo.com/1900408801/5193417988246947",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}

// https://weibo.com/xiaqiu1204
// https://weibo.com/7211561239?tabtype=newVideo
// https://weibo.com/n/%E7%9C%9F%E8%AF%9A%E5%BD%95%E5%88%B6?tabtype=newVideo
//
//	https://weibo.com/u/1686546714?tabtype=newVideo
func TestExtractPlaylist(t *testing.T) {
	playlist, err := ExtractPlaylist(context.Background(), &schema.ExtractReq{
		URL: "https://weibo.com/u/1686546714?tabtype=newVideo",
	})
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(playlist, "", "  ")

	t.Log(string(b))
}
