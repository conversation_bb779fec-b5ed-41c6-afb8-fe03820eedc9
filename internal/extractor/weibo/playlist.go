package weibo

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"github.com/gongyinshi/shared/urlx"
	"net/url"
	"slices"

	"github.com/samber/lo"
	"github.com/tidwall/gjson"
)

// 提取播放列表
func ExtractPlaylist(ctx context.Context, req *schema.ExtractReq) (playlist *schema.Playlist, err error) {
	paths := urlx.ParsePath(req.URL)
	var UID string
	if slices.Contains(paths, "u") {
		UID = paths[slices.Index(paths, "u")+1]
	} else if slices.Contains(paths, "n") {
		UID, err = getUIDByUserDomain(ctx, paths[slices.Index(paths, "n")+1], "screen_name")
		if err != nil {
			return nil, err
		}

	} else if len(paths) == 1 {
		UID, err = getUIDByUserDomain(ctx, paths[0], "custom")
		if err != nil {
			return nil, err
		}
	} else {
		return nil, extract.ErrInvalidUserPageURL
	}
	playlist = &schema.Playlist{}
	params := map[string]string{
		"uid":    UID,
		"cursor": "0",
	}
	if req.Cursor != "" {
		params["cursor"] = req.Cursor
	}
	headers := map[string]string{
		"referer": fmt.Sprintf("https://weibo.com/u/%s?tabtype=newVideo", UID), // TODO cookie
		"cookie":  "XSRF-TOKEN=aaZ2DWVad3jwj8Q8tpB7h-Ue; SUB=_2AkMfcRu_f8NxqwFRmvgQzmrnbo13zwrEieKpLepkJRMxHRl-yT9yqk0NtRB6NPE1UrCLKGVKQsrOpJsZUSRQaK6IcDlF; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9WFmWhnRjN.ykbD8-yzOfTpI; WBPSESS=9-DrhgMbGnVf8No6y5BLAcoXEbZcmuJ8T3cuOv8dK-7ZxknzWNloglCH-u9ncsBWkDLtTj81MaLuWCYPsaxUiZtGliNrczx8sLkwybfEnm12vTpKWQ3HYEHzniLdy9h9I2o_cci6MpWlINKBlx1Gip9oQYY67-umXqQZC5rFCwA=",
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParams(params).
		Get("https://weibo.com/ajax/profile/getWaterFallContent")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("[playlist] weibo:%s", resp.String())
	}
	result := gjson.Parse(resp.String())
	if result.Get("ok").String() != "1" || !result.Get("data").Exists() {
		return playlist, fmt.Errorf("微博主页批量获取数据失败")
	}
	data := result.Get("data")
	if data.Get("next_cursor").Exists() {
		playlist.HasMore = true
		playlist.NextCursor = data.Get("next_cursor").String()
	}
	dataList := data.Get("list").Array()
	for _, v := range dataList {
		mediaInfo := v.Get("page_info.media_info")
		slideCover := v.Get("page_info.slide_cover")
		var videoURL string
		if mediaInfo.Exists() {
			videoURL = lo.CoalesceOrEmpty(
				mediaInfo.Get("mp4_720p_mp4").String(),
				mediaInfo.Get("stream_url_hd").String(),
				mediaInfo.Get("stream_url").String(),
			)

		} else if slideCover.Exists() {
			slideVideos := slideCover.Get("slide_videos")
			if !slideVideos.Exists() {
				continue
			}
			videoURL = slideVideos.Array()[0].Get("url").String()
		} else {
			continue
		}
		post := &schema.Post{
			ID:         v.Get("id").String(),
			CreateTime: int(v.Get("create_time").Int()),
			Text:       v.Get("text_raw").String(),
		}
		media := schema.NewVideoMedia(videoURL, v.Get("page_info.page_pic").String())
		media.Headers = map[string]string{
			"User-Agent": "", // 封面需使用空的UA
		}
		post.Medias = append(post.Medias, media)
		playlist.Posts = append(playlist.Posts, post)
	}
	if len(dataList) != 0 {
		playlist.User = &schema.User{
			Username: dataList[0].Get("user.screen_name").String(),
		}
	}

	return playlist, nil
}

func getUIDByUserDomain(ctx context.Context, userDomain string, paramKey string) (string, error) {
	key, err := url.QueryUnescape(userDomain)
	if err != nil {
		return "", err
	}

	referer := fmt.Sprintf("https://weibo.com/n/%s", userDomain)
	if paramKey == "custom" {
		referer = fmt.Sprintf("https://weibo.com/%s", userDomain)
	}
	headers := map[string]string{
		"referer": referer, // TODO cookie
		"cookie":  "XSRF-TOKEN=aaZ2DWVad3jwj8Q8tpB7h-Ue; SUB=_2AkMfcRu_f8NxqwFRmvgQzmrnbo13zwrEieKpLepkJRMxHRl-yT9yqk0NtRB6NPE1UrCLKGVKQsrOpJsZUSRQaK6IcDlF; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9WFmWhnRjN.ykbD8-yzOfTpI; WBPSESS=9-DrhgMbGnVf8No6y5BLAcoXEbZcmuJ8T3cuOv8dK-7ZxknzWNloglCH-u9ncsBWkDLtTj81MaLuWCYPsaxUiZtGliNrczx8sLkwybfEnm12vTpKWQ3HYEHzniLdy9h9I2o_cci6MpWlINKBlx1Gip9oQYY67-umXqQZC5rFCwA=",
	}
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam(paramKey, key).
		Get("https://weibo.com/ajax/profile/info")
	if err != nil {
		return "", err
	}
	if resp.IsError() {
		return "", fmt.Errorf("[playlist] weibo playlist.getUIDByUserDomain%s", resp.String())
	}
	result := gjson.Parse(resp.String())
	UID := result.Get("data.user.id").String()
	if result.Get("ok").String() != "1" || UID == "" {
		return "", extract.ErrInvalidUserPageURL
	}
	return UID, nil
}
