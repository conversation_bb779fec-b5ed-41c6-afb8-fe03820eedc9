package weibo

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/partner/yijianjiexi"
	"garden/internal/schema"
	"garden/pkg/useragent"
	"regexp"
	"strings"

	"github.com/tidwall/gjson"
)

var (
	reVideoId = regexp.MustCompile(`(?i)(?:show\?fid=|/tv/show/)(\d+:\d+)`) // 视频ID正则
)

const weiboCookie = "login_sid_t=6b652c77c1a4bc50cb9d06b24923210d; cross_origin_proto=SSL; WBStorage=2ceabba76d81138d|undefined; _s_tentry=passport.weibo.com; Apache=7330066378690.048.1625663522444; SINAGLOBAL=7330066378690.048.1625663522444; ULV=1625663522450:1:1:1:7330066378690.048.1625663522444:; TC-V-WEIBO-G0=35846f552801987f8c1e8f7cec0e2230; SUB=_2AkMXuScYf8NxqwJRmf8RzmnhaoxwzwDEieKh5dbDJRMxHRl-yT9jqhALtRB6PDkJ9w8OaqJAbsgjdEWtIcilcZxHG7rw; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9W5Qx3Mf.RCfFAKC3smW0px0; XSRF-TOKEN=JQSK02Ijtm4Fri-YIRu0-vNj"

// 单个帖子提取
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	matches := reVideoId.FindStringSubmatch(req.URL)
	if len(matches) > 1 {
		return extractWeiboVideo(ctx, matches[1])
	} else {
		// 优先提取无水印的图片  https://weibo.com/1900408801/5193417988246947
		post, err := yijianjiexi.Extract(ctx, req)
		if err == nil {
			return post, nil
		}
		return extractWeiboPost(ctx, req.URL)
	}
}

// http://weibo.com/2142058927/Eg0OBB5A5
// https://weibo.com/7790166853/4937617801481842
// https://weibo.com/1900408801/5193417988246947  有水印
// https://m.weibo.cn/status/4114395665972373
func extractWeiboPost(ctx context.Context, url string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderUserAgent, useragent.RandomMobile()).
		Get(url)
	if err != nil {
		return nil, err
	}

	_, after, found := strings.Cut(resp.String(), "render_data = [")
	if !found {
		return nil, fmt.Errorf("无法解析微博帖子数据")
	}
	data, _, found := strings.Cut(after, "}]")
	if !found {
		return nil, fmt.Errorf("无法解析微博帖子数据")
	}
	status := gjson.Get(data+"}", "status")

	// 文案
	text := status.Get("page_info.content2").String()
	if text == "" {
		text = status.Get("status_title").String()
	}
	post := &schema.Post{
		Text: text,
	}

	// 视频
	pageInfo := status.Get("page_info")
	if pageInfo.Exists() {
		videoURL := pageInfo.Get("media_info.stream_url_hd").String()
		if videoURL == "" {
			videoURL = pageInfo.Get("media_info.stream_url").String()
		}
		if videoURL != "" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, pageInfo.Get("page_pic.url").String()))
		}
	}

	// 图集 (多视频)
	status.Get("pics").ForEach(func(_, pic gjson.Result) bool {
		videoURL := pic.Get("videoSrc").String()
		imageURL := pic.Get("large.url").String()
		if imageURL == "" {
			imageURL = pic.Get("url").String()
		}
		if videoURL != "" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, imageURL))
		} else {
			post.Medias = append(post.Medias, schema.NewImageMedia(imageURL))
		}
		return true
	})

	if post.IsEmpty() {
		return nil, fmt.Errorf("不包含图片视频")
	}

	return post, nil
}

// https://video.weibo.com/show?fid=1034:4757578882941075
// https://weibo.com/tv/show/1034:4830681801621538
func extractWeiboVideo(ctx context.Context, videoID string) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderCookie, weiboCookie).
		SetHeader(constant.HeaderReferer, fmt.Sprintf("https://h5.video.weibo.com/show/%s", videoID)).
		SetHeader(constant.HeaderContentType, "application/x-www-form-urlencoded").
		SetHeader(constant.HeaderUserAgent, useragent.Mobile()).
		SetBody([]byte(`data={"Component_Play_Playinfo":{"oid":"` + videoID + `"}}`)).
		Post(fmt.Sprintf("https://h5.video.weibo.com/api/component?page=/show/%s", videoID))
	if err != nil {
		return nil, err
	}
	data := gjson.GetBytes(resp.Bytes(), "data.Component_Play_Playinfo")
	// 获取最高清晰度视频URL
	var videoURL string
	data.Get("urls").ForEach(func(key, value gjson.Result) bool {
		// 第一个是最高清晰度
		videoURL = value.String()
		return videoURL == "" // 返回false停止遍历, 返回true继续遍历
	})

	return &schema.Post{
		Text: data.Get("title").String(),
		Medias: []*schema.Media{
			schema.NewVideoMedia(videoURL, data.Get("cover_image").String()),
		},
	}, nil
}
