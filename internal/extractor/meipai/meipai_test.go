package meipai

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// http://www.meipai.com/video/090/1106395083
// http://www.meipai.com/video/625/6992773354391367966
// https://www.meipai.com/media/6773464781112241812
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "http://www.meipai.com/video/625/6992773354391367966",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}

func TestDecodeVideoURL(t *testing.T) {
	videoURL, err := decodeVideoURL("1234")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(videoURL)
}
