package meipai

import (
	"context"
	"encoding/base64"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"garden/pkg/useragent"
	"strconv"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	if strings.Contains(req.URL, "live/") {
		return nil, extract.ErrLiveStreamNotSupported
	}

	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderUserAgent, useragent.RandomDesktop()).
		Get(req.URL)
	if err != nil {
		return nil, err
	}
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	videoNode := doc.Find("body .detail-content .detail-left #detailVideo")
	if videoNode.Length() == 0 {
		return nil, fmt.Errorf("未找到视频信息")
	}

	// 获取视频地址
	videoURL := videoNode.AttrOr("data-video", "")
	if videoURL == "" {
		return nil, fmt.Errorf("未找到视频地址")
	}
	// 解码视频地址
	videoURL, err = decodeVideoURL(videoURL)
	if err != nil {
		return nil, err
	}
	// 获取封面地址
	coverURL := videoNode.Find("img").AttrOr("src", "")
	if idx := strings.Index(coverURL, "!"); idx != -1 {
		coverURL = coverURL[:idx]
	}
	return &schema.Post{
		Text: doc.Find("meta[name='description']").AttrOr("content", ""),
		Medias: []*schema.Media{
			schema.NewVideoMedia(videoURL, coverURL),
		},
	}, nil
}

func decodeVideoURL(encodedURL string) (decodedURL string, err error) {
	// 使用recover捕获未知的panic，比如输入的content不是有效的编码URL
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("decode meipai url failed: %v", r)
		}
	}()

	// 提取前4个字节并反转
	headerBytes := []byte{encodedURL[0], encodedURL[1], encodedURL[2], encodedURL[3]}
	for i, j := 0, len(headerBytes)-1; i < j; i, j = i+1, j-1 {
		headerBytes[i], headerBytes[j] = headerBytes[j], headerBytes[i]
	}

	// 将十六进制转换为十进制
	hexStr := string(headerBytes)
	decNum, err := strconv.ParseInt(hexStr, 16, 64)
	if err != nil {
		return "", fmt.Errorf("parse hex failed: %w", err)
	}
	decStr := strconv.FormatInt(decNum, 10)

	// 提取前缀和后缀
	prefix := []byte{decStr[0], decStr[1]}
	suffix := []byte(decStr[2:])

	// 处理字符串
	contentBody := encodedURL[4:]
	firstPart := contentBody[:int(prefix[0]-'0')]
	secondPart := contentBody[int(prefix[0]-'0') : int(prefix[0]-'0')+int(prefix[1]-'0')]

	// 第一次替换
	firstResult := firstPart + strings.ReplaceAll(contentBody[int(prefix[0]-'0'):], secondPart, "")

	// 计算新的分割点
	suffix[0] = byte(len(firstResult) - int(suffix[0]-'0') - int(suffix[1]-'0'))

	// 第二次替换
	partA := firstResult[:int(suffix[0])]
	partB := firstResult[int(suffix[0]) : int(suffix[0])+int(suffix[1]-'0')]
	finalStr := partA + strings.ReplaceAll(firstResult[int(suffix[0]):], partB, "")

	// Base64解码
	decoded, err := base64.StdEncoding.DecodeString(finalStr)
	if err != nil {
		return "", fmt.Errorf("base64 decode failed: %w", err)
	}

	return string(decoded), nil
}
