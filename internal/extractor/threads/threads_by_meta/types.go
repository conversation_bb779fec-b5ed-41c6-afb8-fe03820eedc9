package threads_by_meta

type postResult struct {
	LineType             string `json:"line_type"`
	Post                 Post   `json:"post"`
	ReplyFacepileUsers   []any  `json:"reply_facepile_users"`
	ShouldShowRepliesCta bool   `json:"should_show_replies_cta"`
}

type Post struct {
	AccessibilityCaption string `json:"accessibility_caption"`
	Audio                any    `json:"audio"`
	Caption              struct {
		Text string `json:"text"`
	} `json:"caption"`
	CaptionAddOn    any             `json:"caption_add_on"`
	CaptionIsEdited bool            `json:"caption_is_edited"`
	CarouselMedias  []CarouselMedia `json:"carousel_media"`
	Code            string          `json:"code"`
	GiphyMediaInfo  any             `json:"giphy_media_info"`
	HasAudio        any             `json:"has_audio"`
	HasLiked        bool            `json:"has_liked"`
	ID              string          `json:"id"`
	ImageVersions2  struct {
		Candidates []Candidate `json:"candidates"`
	} `json:"image_versions2"`
	IsFbOnly                  any    `json:"is_fb_only"`
	IsInternalOnly            any    `json:"is_internal_only"`
	IsPaidPartnership         any    `json:"is_paid_partnership"`
	LikeAndViewCountsDisabled bool   `json:"like_and_view_counts_disabled"`
	LikeCount                 int    `json:"like_count"`
	MediaOverlayInfo          any    `json:"media_overlay_info"`
	MediaType                 int    `json:"media_type"`
	OrganicTrackingToken      string `json:"organic_tracking_token"`
	OriginalHeight            int    `json:"original_height"`
	OriginalWidth             int    `json:"original_width"`
	Pk                        string `json:"pk"`
	TakenAt                   int    `json:"taken_at"`
	TextPostAppInfo           struct {
		CanReply              bool `json:"can_reply"`
		DirectReplyCount      int  `json:"direct_reply_count"`
		FediverseInfo         any  `json:"fediverse_info"`
		HushInfo              any  `json:"hush_info"`
		IsPostUnavailable     bool `json:"is_post_unavailable"`
		IsReply               bool `json:"is_reply"`
		LinkPreviewAttachment any  `json:"link_preview_attachment"`
		PinnedPostInfo        struct {
			IsPinnedToParentPost bool `json:"is_pinned_to_parent_post"`
			IsPinnedToProfile    bool `json:"is_pinned_to_profile"`
		} `json:"pinned_post_info"`
		PostUnavailableReason any `json:"post_unavailable_reason"`
		ReplyToAuthor         any `json:"reply_to_author"`
		ShareInfo             struct {
			Typename               string `json:"__typename"`
			CanQuotePost           bool   `json:"can_quote_post"`
			CanRepost              bool   `json:"can_repost"`
			IsRepostedByViewer     bool   `json:"is_reposted_by_viewer"`
			QuotedPost             any    `json:"quoted_post"`
			RepostRestrictedReason string `json:"repost_restricted_reason"`
			RepostedPost           any    `json:"reposted_post"`
		} `json:"share_info"`
		SpecialEffectsEnabledStr string `json:"special_effects_enabled_str"`
	} `json:"text_post_app_info"`
	TextWithEntities  any `json:"text_with_entities"`
	TranscriptionData any `json:"transcription_data"`
	User              struct {
		FriendshipStatus           any    `json:"friendship_status"`
		ID                         string `json:"id"`
		IsVerified                 bool   `json:"is_verified"`
		Pk                         string `json:"pk"`
		ProfilePicURL              string `json:"profile_pic_url"`
		TransparencyLabel          any    `json:"transparency_label"`
		TransparencyProduct        any    `json:"transparency_product"`
		TransparencyProductEnabled bool   `json:"transparency_product_enabled"`
		Username                   string `json:"username"`
	} `json:"user"`
	VideoVersions []VideoVersion `json:"video_versions"`
}

type CarouselMedia struct {
	ImageVersions2 struct {
		Candidates []Candidate `json:"candidates"`
	} `json:"image_versions2"`
	VideoVersions []VideoVersion `json:"video_versions"`
}

type VideoVersion struct {
	Candidates []Candidate `json:"candidates"`
	Type       int         `json:"type"`
	URL        string      `json:"url"`
}

type Candidate struct {
	Height int    `json:"height"`
	URL    string `json:"url"`
	Width  int    `json:"width"`
}
