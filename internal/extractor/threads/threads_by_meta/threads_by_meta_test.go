package threads_by_meta

import (
	"context"
	"garden/internal/schema"
	"testing"
)

// https://www.threads.net/t/CuYOIBwrgJ6/
// https://www.threads.net/t/CuYI0dXJyez/
// https://www.threads.net/t/CuX3tfAuAec/?igshid=NTc4MTIwNjQ2YQ==
// https://www.threads.net/@ijustine?igshid=NTc4MTIwNjQ2YQ==
// https://www.threads.net/@cnn/post/CvdxUTTOAcj/
func TestExtract(t *testing.T) {
	post, err := Extract(context.Background(), &schema.ExtractReq{
		URL: "https://www.threads.net/t/CuX3tfAuAec/?igshid=NTc4MTIwNjQ2YQ==",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}
