package threads_by_meta

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/extract"
	"regexp"
)

const postAPI = "https://threads-by-meta-threads-an-instagram-app-detailed.p.rapidapi.com/get_post_with_web_id"

var headers = map[string]string{
	"x-rapidapi-host": "threads-by-meta-threads-an-instagram-app-detailed.p.rapidapi.com",
	"x-rapidapi-key":  "**************************************************",
}

var webIDRegex = regexp.MustCompile(`/(t|post)/([^/?]+)`)

// https://www.threads.net/t/CuYOIBwrgJ6/
// https://www.threads.net/t/CuYI0dXJyez/
// https://www.threads.net/t/CuX3tfAuAec/?igshid=NTc4MTIwNjQ2YQ==
// https://www.threads.net/@cnn/post/CvdxUTTOAcj/
// https://www.threads.net/@ijustine?igshid=NTc4MTIwNjQ2YQ==
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	matches := webIDRegex.FindStringSubmatch(req.URL)
	if len(matches) < 3 {
		return nil, extract.ErrUnsupportedURL
	}
	webID := matches[2]

	var result postResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeaders(headers).
		SetQueryParam("web_id", webID).
		Get(postAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("threads_by_meta: %s", resp.String())
	}

	// 不支持纯文字  https://www.threads.net/t/CuYgRTqutfb/?igshid=NTc4MTIwNjQ2YQ==
	data := result.Post
	medias := getMediaList(data)

	return &schema.Post{
		Text:   data.Caption.Text,
		Medias: medias,
	}, nil
}

func getMediaListFromVideoVersions(videoVersions []VideoVersion, imageURL string) []*schema.Media {
	medias := make([]*schema.Media, 0)
	var resourceURL string

	if videoVersions[0].URL != "" {
		resourceURL = videoVersions[0].URL
	} else {
		resourceURL = ""
	}

	var media *schema.Media
	if resourceURL != "" {
		media = schema.NewVideoMedia(resourceURL, imageURL)
	} else {
		media = schema.NewImageMedia(imageURL)
	}
	medias = append(medias, media)
	return medias
}

func getMediaList(post Post) []*schema.Media {
	medias := make([]*schema.Media, 0)
	imageURL := extractCover(post.ImageVersions2.Candidates)
	if len(post.CarouselMedias) > 0 {
		for _, media := range post.CarouselMedias {
			imageURL = extractCover(media.ImageVersions2.Candidates)
			if len(media.VideoVersions) > 0 {
				medias = append(medias, getMediaListFromVideoVersions(media.VideoVersions, imageURL)...)
			} else if len(media.ImageVersions2.Candidates) > 0 {
				medias = append(medias, schema.NewImageMedia(imageURL))
			} else {
				return nil
			}
		}
	} else if len(post.VideoVersions) > 0 {
		medias = append(medias, getMediaListFromVideoVersions(post.VideoVersions, imageURL)...)
	} else if len(post.ImageVersions2.Candidates) > 0 {
		medias = append(medias, schema.NewImageMedia(imageURL))
	} else {
		return nil
	}
	return medias
}

func extractCover(candidates []Candidate) string {
	if len(candidates) > 0 {
		return candidates[0].URL
	}
	return ""
}
