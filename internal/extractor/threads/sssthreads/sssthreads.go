package sssthreads

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/schema"
	"garden/pkg/useragent"

	"github.com/tidwall/gjson"
)

// https://sssthreads.pro/
const api = "https://api.threadsphotodownloader.com/v2/media"

// https://www.threads.com/@aracalderon_831/post/DMlyPn6yQKs
// https://www.threads.net/t/CuYOIBwrgJ6/
// https://www.threads.net/t/CuYI0dXJyez/
// https://www.threads.net/t/CuX3tfAuAec/?igshid=NTc4MTIwNjQ2YQ==
// https://www.threads.net/@cnn/post/CvdxUTTOAcj/
// 不支持主页: https://www.threads.net/@ijustine?igshid=NTc4MTIwNjQ2YQ==
func Extract(ctx context.Context, req *schema.ExtractReq) (*schema.Post, error) {
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetHeader(constant.HeaderOrigin, "https://sssthreads.pro").
		SetHeader(constant.HeaderReferer, "https://sssthreads.pro/").
		SetHeader(constant.HeaderUserAgent, useragent.RandomDesktop()).
		SetQueryParam("url", req.URL).
		Get(api)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("sssthreads: %s", resp.String())
	}

	data := gjson.ParseBytes(resp.Bytes())
	post := &schema.Post{}
	data.Get("video_urls").ForEach(func(key, value gjson.Result) bool {
		videoURL := value.Get("download_url").String()
		if videoURL != "" {
			post.Medias = append(post.Medias, schema.NewVideoMedia(videoURL, ""))
		}
		return true
	})

	// 图集
	data.Get("image_urls").ForEach(func(key, value gjson.Result) bool {
		post.Medias = append(post.Medias, schema.NewImageMedia(value.String()))
		return true
	})
	return post, nil
}
