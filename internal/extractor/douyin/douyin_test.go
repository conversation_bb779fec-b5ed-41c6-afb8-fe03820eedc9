package douyin

import (
	"context"
	"testing"
)

// 存在的合集: https://www.douyin.com/collection/6814784290748925960/1?previous_page=discover
// 不存在的合集: https://www.douyin.com/collection/68147842905960
// 主页: https://www.douyin.com/user/MS4wLjABAAAAty81qnV2oRFVzd-wWb4mMdHeIKrOifHxt3VROZ9pJmVQhsdDYJ4TbN6rWzo7g6kl
// TODO: 未测试包含图集帖子的合集
func TestExtractCollection(t *testing.T) {
	result, err := ExtractCollection(context.Background(), "68147842905960", "10")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}
