package douyin

import (
	"context"
	"fmt"
	"garden/internal/constant"
	"garden/internal/extractor/tiktok/tiktok_best_experience"
	"garden/internal/schema"
	"garden/pkg/extract"
	"log/slog"
	"strconv"

	"github.com/samber/lo"
)

const collectionAPI = "https://www.iesdouyin.com/web/api/mix/item/list/"

type collectionResult struct {
	Cursor     int                                  `json:"cursor"`
	Has<PERSON>ore    bool                                 `json:"has_more"`
	AwemeList  []tiktok_best_experience.AwemeDetail `json:"aweme_list"`
	StatusCode int                                  `json:"status_code"` // 0: 成功 其他: 失败
	StatusMsg  string                               `json:"status_msg"`  // 失败原因
}

func ExtractCollection(ctx context.Context, id, cursor string) (*schema.Playlist, error) {
	var result collectionResult
	resp, err := constant.RestyClient.R().SetContext(ctx).
		SetQueryParam("mix_id", id).
		SetQueryParam("cursor", cursor).
		SetQueryParam("count", "50").
		SetResult(&result).
		Get(collectionAPI)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		slog.Error("抖音合集提取失败", slog.Any("id", id), slog.Any("cursor", cursor), slog.Any("response", resp.String()))
		return nil, extract.ErrRetryable // 可能是被限流
	}
	if result.StatusCode != 0 {
		return nil, fmt.Errorf("抖音合集提取失败: %s", result.StatusMsg)
	}
	playlist := &schema.Playlist{}
	if result.HasMore {
		playlist.HasMore = true
		playlist.NextCursor = strconv.Itoa(result.Cursor)
	}
	// 帖子列表
	playlist.Posts = tiktok_best_experience.ConvertToPosts(result.AwemeList)
	// 合集信息
	if len(result.AwemeList) > 0 {
		playlist.User = &schema.User{
			Username: result.AwemeList[0].MixInfo.MixName,
			Avatar:   lo.FirstOrEmpty(result.AwemeList[0].MixInfo.CoverURL.URLList),
		}
	}
	return playlist, nil
}
