# This workflow will build a docker container, publish and deploy it to Tencent Kubernetes Engine (TKE) when there is a push to the "main" branch.

name: 发布到生产环境 🚀

on:
  workflow_dispatch:
  # push:
  #   branches: ["main"]

# Environment variables available to all jobs and steps in this workflow
env:
  ACR_IMAGE_URL: registry.cn-shenzhen.aliyuncs.com/gystech/garden
  DEPLOYMENT_NAME: garden
  NAMESPACE: garden

permissions:
  contents: read

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Generate Image Tag
        id: tag
        run: |-
          TAG=$(TZ='Asia/Shanghai' date +%Y%m%d%H%M)-${GITHUB_SHA::6}
          echo "TAG=$TAG" >> $GITHUB_OUTPUT
          echo "Generated image tag: $TAG"

      - name: Build and Push Docker image
        run: |-
          echo "${{ secrets.ACR_REGISTRY_PASSWORD }}" | docker login -u ${{ vars.ALIYUN_ACCOUNT_ID }} --password-stdin ${ACR_IMAGE_URL}
          docker build -t ${ACR_IMAGE_URL}:${{ steps.tag.outputs.TAG }} .
          docker push ${ACR_IMAGE_URL}:${{ steps.tag.outputs.TAG }}

      - name: Set up SSH key
        run: |-
          mkdir -p ~/.ssh
          echo "${{ secrets.HK_MAIN_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "Host ${{ vars.HK_MAIN_IP }}
            IdentityFile ~/.ssh/id_rsa
            User ubuntu" > ~/.ssh/config
          echo "Host ${{ vars.GZ_MAIN_IP }}
            IdentityFile ~/.ssh/id_rsa
            User ubuntu" >> ~/.ssh/config  
          ssh-keyscan -H ${{ vars.HK_MAIN_IP }} >> ~/.ssh/known_hosts
          ssh-keyscan -H ${{ vars.GZ_MAIN_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to TKE
        run: |-
          cd .k8s/base
          kustomize edit set image ${ACR_IMAGE_URL}:${{ steps.tag.outputs.TAG }}
          echo "Deploying to hk..."
          kustomize build ../overlays/hk > manifests.yaml
          ssh ${{ vars.HK_MAIN_IP }} "kubectl apply -f -" < manifests.yaml && \
          ssh ${{ vars.HK_MAIN_IP }} "kubectl get services -o wide -n ${NAMESPACE}"
          echo "Deploying to gz..."
          kustomize build ../overlays/gz > manifests.yaml
          ssh ${{ vars.GZ_MAIN_IP }} "kubectl apply -f -" < manifests.yaml && \
          ssh ${{ vars.GZ_MAIN_IP }} "kubectl get services -o wide -n ${NAMESPACE}"
          # echo "Deploying to las-vegas..."
          # kustomize build ../overlays/las-vegas > manifests.yaml
          # ssh ${{ vars.LAS_VEGAS_IP }} "kubectl apply -f -" < manifests.yaml && \
          # ssh ${{ vars.LAS_VEGAS_IP }} "kubectl get services -o wide -n ${NAMESPACE}"

      - name: Clean up SSH key
        if: always()
        run: |-
          rm -f ~/.ssh/id_rsa
          rm -f ~/.ssh/config
