apiVersion: apps/v1
kind: Deployment
metadata:
  name: garden
  labels:
    app: garden
spec:
  replicas: 3
  selector:
    matchLabels:
      app: garden
  template:
    metadata:
      labels:
        app: garden
      annotations:
        prometheus.io/scrape: "true"
    spec:
      containers:
        - name: garden
          image: registry.cn-shenzhen.aliyuncs.com/gystech/garden:latest
          ports:
            - containerPort: 80
          env:
            - name: REDIS_URL
              value: redis://redis:6379
            - name: NODE_NAME
              valueFrom:
                configMapKeyRef:
                  name: garden-config
                  key: NODE_NAME
          volumeMounts:
            - mountPath: /cache
              name: cache
      imagePullSecrets:
        - name: acr-docker-registry
      volumes:
        - name: cache
          hostPath:
            path: /var/cache/garden
            type: DirectoryOrCreate
---
apiVersion: v1
kind: Service
metadata:
  name: garden-go
spec:
  selector:
    app: garden
  ports:
    - port: 80
      targetPort: 80
      nodePort: 30080
  type: NodePort
